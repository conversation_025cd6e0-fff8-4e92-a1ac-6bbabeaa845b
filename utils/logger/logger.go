package logger

import (
	"fmt"
	"log"
	"os"
	"time"

	"fastserver.com/fastserver/config"
)

var (
	// 定义不同级别的日志前缀
	infoPrefix  = "[INFO] "
	debugPrefix = "[DEBUG] "
	warnPrefix  = "[WARN] "
	errorPrefix = "[ERROR] "

	// 标准日志记录器
	infoLogger  = log.New(os.Stdout, infoPrefix, log.LstdFlags)
	debugLogger = log.New(os.Stdout, debugPrefix, log.LstdFlags)
	warnLogger  = log.New(os.Stdout, warnPrefix, log.LstdFlags)
	errorLogger = log.New(os.Stderr, errorPrefix, log.LstdFlags)
)

// Info 记录信息级别的日志
func Info(format string, v ...interface{}) {
	infoLogger.Output(2, fmt.Sprintf(format, v...))
}

// Debug 记录调试级别的日志，只在开发环境中输出
func Debug(format string, v ...interface{}) {
	if config.GetConfig().Server.IsDevelopment() {
		timestamp := time.Now().Format("2006-01-02 15:04:05.000")
		debugLogger.Output(2, fmt.Sprintf("[%s] %s", timestamp, fmt.Sprintf(format, v...)))
	}
}

// Warn 记录警告级别的日志
func Warn(format string, v ...interface{}) {
	warnLogger.Output(2, fmt.Sprintf(format, v...))
}

// Error 记录错误级别的日志
func Error(format string, v ...interface{}) {
	errorLogger.Output(2, fmt.Sprintf(format, v...))
}

// UnderlineText 为文本添加下划线效果
func UnderlineText(text string) string {
	// 使用ANSI转义序列添加下划线效果
	return "\033[4m" + text + "\033[0m"
}
