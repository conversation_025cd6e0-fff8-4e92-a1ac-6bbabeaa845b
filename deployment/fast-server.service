[Unit]
Description=Fast Server STT Service
After=network.target

[Service]
Type=simple
User=fast-server
Group=fast-server
WorkingDirectory=/opt/fast-server
ExecStart=/opt/fast-server/fastserver
Restart=always
RestartSec=5

# ADC配置 - 服务账号JSON文件路径
Environment=GOOGLE_APPLICATION_CREDENTIALS=/opt/fast-server/credentials/fast-server-credentials.json

# 其他环境变量
Environment=PORT=8080
Environment=ENV=production

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/fast-server/logs

[Install]
WantedBy=multi-user.target
