package timeutil

import (
	"strconv"
	"time"

	"fastserver.com/fastserver/utils/logger"
)

// TimeFormat 是Go的参考时间格式
const TimeFormat = "2006-01-02 15:04:05"

// ParseClientTime 将客户端时间字符串解析为时间戳
// 如果解析失败或客户端时间为空，则返回当前服务器时间
func ParseClientTime(clientTime string) int64 {
	if clientTime == "" {
		return time.Now().UnixMilli()
	}

	millis, err := strconv.ParseInt(clientTime, 10, 64)
	if err != nil {
		logger.Debug("Failed to parse client time: %v, using server time", err)
		return time.Now().UnixMilli()
	}

	return millis
}

// FormatMillis 将毫秒时间戳格式化为字符串
func FormatMillis(millis int64) string {
	return time.UnixMilli(millis).Format(TimeFormat)
}

// ParseTimeWithTimezone 使用指定时区解析时间字符串
func ParseTimeWithTimezone(timeStr string, timezone string) (time.Time, error) {
	// 默认使用本地时区
	loc := time.Local

	// 尝试加载客户端时区
	if timezone != "" {
		// 处理可能包含额外信息的时区字符串
		tzName := timezone
		if idx := indexOf(timezone, ";"); idx >= 0 {
			tzName = timezone[:idx]
		}

		if tzLoc, err := time.LoadLocation(tzName); err == nil {
			loc = tzLoc
		} else {
			logger.Debug("加载时区失败: %v, 使用本地时区", err)
		}
	}

	// 使用客户端时区解析时间
	return time.ParseInLocation(TimeFormat, timeStr, loc)
}

// indexOf 返回子字符串在字符串中的位置，如果不存在则返回-1
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// GetTimeWithTimezone combines timestamp and timezone to get a time.Time object.
func GetTimeWithTimezone(clientTime, clientTimezone string) (time.Time, error) {
	millis := ParseClientTime(clientTime)
	loc, err := ParseTimezone(clientTimezone)
	if err != nil {
		return time.Time{}, err
	}
	return time.UnixMilli(millis).In(loc), nil
}

// ParseTimezone parses a timezone string and returns a time.Location.
func ParseTimezone(timezone string) (*time.Location, error) {
	if timezone == "" {
		return time.Local, nil
	}

	tzName := timezone
	if idx := indexOf(timezone, ";"); idx >= 0 {
		tzName = timezone[:idx]
	}

	loc, err := time.LoadLocation(tzName)
	if err != nil {
		logger.Debug("Failed to load timezone: %v, using local time", err)
		return time.Local, nil // Fallback to local time
	}
	return loc, nil
}
