package task

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"fastserver.com/fastserver/ai"

	"encoding/base64"

	"connectrpc.com/connect"
	"fastserver.com/fastserver/ai/impl"
	"fastserver.com/fastserver/biz/task/timeutil"
	"fastserver.com/fastserver/config"
	v1 "fastserver.com/fastserver/gen/proto/task/v1"
	v1connect "fastserver.com/fastserver/gen/proto/task/v1/v1connect"
	"fastserver.com/fastserver/utils/logger"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	maxInputLength = 500
)

// TaskAnalyzer 实现任务分析服务
type TaskAnalyzer struct {
	llm ai.LLMProvider
	stt ai.STTProvider
}

// NewTaskAnalyzer 创建一个新的任务分析器
func NewTaskAnalyzer(llm ai.LLMProvider, stt ai.STTProvider) *TaskAnalyzer {
	return &TaskAnalyzer{llm: llm, stt: stt}
}

// 确保 TaskAnalyzer 实现 v1connect.TaskServiceHandler 接口
var _ v1connect.TaskServiceHandler = (*TaskAnalyzer)(nil)

// ParsedTask 定义解析结果的结构体
type ParsedTask struct {
	Time                time.Time       // 任务执行时间（首次执行时间）
	Repeatable          bool            // 是否为重复任务
	RepeatTimes         int32           // 重复次数
	RepeatIntervalValue int32           // 重复周期值
	RepeatIntervalUnit  string          // 重复周期单位
	Plugin              string          // 插件名称
	Search              string          // 搜索内容
	Name                string          // 任务名称
	Methods             []string        // 使用的解析方法
	TokenUsage          impl.TokenUsage // Token 使用情况
}

// 创建一个字符串到枚举值的映射
var repeatIntervalUnitMap = map[string]v1.RepeatIntervalUnit{
	"day":   v1.RepeatIntervalUnit_DAY,
	"week":  v1.RepeatIntervalUnit_WEEK,
	"month": v1.RepeatIntervalUnit_MONTH,
	"year":  v1.RepeatIntervalUnit_YEAR,
}

// stringToRepeatIntervalUnit 将字符串转换为重复间隔单位枚举值
func stringToRepeatIntervalUnit(unit string) v1.RepeatIntervalUnit {
	if val, ok := repeatIntervalUnitMap[strings.ToLower(unit)]; ok {
		return val
	}
	return v1.RepeatIntervalUnit_REPEAT_INTERVAL_UNIT_UNSPECIFIED
}

// Analyze 实现任务分析服务的主要方法
func (t *TaskAnalyzer) Analyze(
	ctx context.Context,
	req *connect.Request[v1.AnalyzeRequest],
) (*connect.Response[v1.AnalyzeResponse], error) {
	input := req.Msg.Input
	if err := validateInput(input); err != nil {
		return nil, err
	}

	clientTime := req.Header().Get("bluespace-timestamp")
	clientTimezone := req.Header().Get("bluespace-timezone")

	logger.Debug("clientTime: %s, clientTimezone: %s", clientTime, clientTimezone)

	// 统一解析任务
	parsedTask, err := t.parseTaskUnified(input, clientTime, clientTimezone)
	if err != nil {
		return nil, fmt.Errorf("failed to parse task: %v", err)
	}

	// 构建响应
	return buildAnalyzeResponse(input, parsedTask, clientTimezone), nil
}

// validateInput 验证输入是否有效
func validateInput(input string) error {
	if len(input) == 0 {
		return fmt.Errorf("input is empty")
	}
	if len(input) >= maxInputLength {
		return fmt.Errorf("input is too long")
	}
	return nil
}

// buildAnalyzeResponse 构建分析响应
func buildAnalyzeResponse(input string, parsedTask ParsedTask, clientTimezone string) *connect.Response[v1.AnalyzeResponse] {
	// 使用输入作为默认任务名
	taskName := input
	if parsedTask.Name != "" {
		taskName = parsedTask.Name
	}

	// 将解析方法转换为字符串
	methodsStr := fmt.Sprintf("%v", parsedTask.Methods)

	return connect.NewResponse(&v1.AnalyzeResponse{
		Task: &v1.SimpleTask{
			Id:                  fmt.Sprintf("%d", time.Now().UnixMilli()),
			Name:                taskName,
			Search:              parsedTask.Search,
			StartTime:           timestamppb.New(parsedTask.Time),
			Repeatable:          parsedTask.Repeatable,
			RepeatTimes:         parsedTask.RepeatTimes,
			RepeatIntervalValue: parsedTask.RepeatIntervalValue,
			RepeatIntervalUnit:  stringToRepeatIntervalUnit(parsedTask.RepeatIntervalUnit),
			Description:         input,
			Plugin:              parsedTask.Plugin,
			Timezone:            clientTimezone,
		},
		Methods: methodsStr,
		DetailedTokenUsage: &v1.TokenUsage{
			PromptTokens:     int32(parsedTask.TokenUsage.PromptTokens),
			CompletionTokens: int32(parsedTask.TokenUsage.CompletionTokens),
			TotalTokens:      int32(parsedTask.TokenUsage.TotalTokens),
		},
	})
}

// parseTaskUnified 统一解析任务
func (t *TaskAnalyzer) parseTaskUnified(taskInput, clientTime, clientTimezone string) (ParsedTask, error) {
	// 初始化解析结果
	parsedTask := ParsedTask{
		Methods:    []string{"unified"},
		TokenUsage: impl.TokenUsage{},
	}

	// 使用工具类解析客户端时间，并格式化为 RFC3339 字符串
	currentTime, err := timeutil.GetTimeWithTimezone(clientTime, clientTimezone)
	if err != nil {
		return parsedTask, fmt.Errorf("failed to parse client time: %v", err)
	}
	currentTimeStr := currentTime.Format(time.RFC3339)

	// 获取并执行主要分析
	result, err := t.executeMainAnalysis(context.Background(), taskInput, currentTimeStr, currentTime, clientTimezone)
	// updateTokenUsage(&parsedTask.TokenUsage, tokenUsage) // 如需统计 token 可扩展

	if err != nil {
		return parsedTask, err
	}

	// 解析 AI 返回的 JSON 结果
	parsedResult, err := parseAIResponse(result)
	if err != nil {
		return parsedTask, fmt.Errorf("failed to parse AI response: %v", err)
	}

	// 使用工具类解析时间
	parsedTime, err := timeutil.ParseTimeWithTimezone(parsedResult.Time, clientTimezone)
	if err != nil {
		logger.Debug("解析时间失败: %v", err)
		parsedTime = time.Time{}
	}

	logger.Debug("解析时间: %v, 时区: %v", parsedTime, time.Local)

	// 更新解析结果
	parsedTask.Time = parsedTime
	parsedTask.Repeatable = parsedResult.Repeatable
	parsedTask.RepeatTimes = parsedResult.RepeatTimes
	parsedTask.RepeatIntervalValue = parsedResult.RepeatIntervalValue
	parsedTask.RepeatIntervalUnit = parsedResult.RepeatIntervalUnit
	parsedTask.Plugin = parsedResult.Plugin
	parsedTask.Search = parsedResult.Search
	parsedTask.Name = parsedResult.Name

	// 对缺失字段进行重试解析
	t.retryMissingFields(&parsedTask, taskInput, clientTime)

	return parsedTask, nil
}

// executeMainAnalysis 执行主要分析
func (t *TaskAnalyzer) executeMainAnalysis(ctx context.Context, taskInput, currentTimeRFC3339 string, currentTime time.Time, clientTimezone string) (string, error) {
	systemPrompt := config.GetPrompt("task_analyzer.system")
	systemPrompt += "\nThe 'time' field in your response MUST be a future time relative to the CurrentTime."

	// 格式化人类可读的时间字符串
	currentTimeString := currentTime.Format("2006-01-02 15:04:05")

	userPrompt := config.GetPromptTemplate("task_analyzer.user_template", map[string]string{
		"CurrentTime":       currentTimeRFC3339,
		"CurrentTimeString": currentTimeString,
		"CurrentTimezone":   clientTimezone,
		"TaskInput":         taskInput,
	})

	logger.Debug("Assembled user prompt: %s", userPrompt)

	req := ai.CompletionRequest{
		Messages: []ai.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Model: config.GetConfig().AI.Model,
	}
	resp, err := t.llm.Completion(ctx, req)
	if err != nil {
		return "", err
	}
	logger.Debug("AI 返回的原始内容: %s", resp.Content)
	return resp.Content, nil
}

// AIResponseResult AI 响应结果结构
type AIResponseResult struct {
	Time                string `json:"time"`
	Plugin              string `json:"plugin"`
	Search              string `json:"search"`
	Name                string `json:"name"`
	Repeatable          bool   `json:"repeatable"`
	RepeatTimes         int32  `json:"repeat_times"`
	RepeatIntervalValue int32  `json:"repeat_interval_value"`
	RepeatIntervalUnit  string `json:"repeat_interval_unit"`
}

// parseAIResponse 解析 AI 响应
func parseAIResponse(result string) (AIResponseResult, error) {
	var parsedResult AIResponseResult

	// 清理 AI 返回的内容，确保是有效的 JSON
	cleanedResult := extractJSON(result)

	// 解析 JSON
	err := json.Unmarshal([]byte(cleanedResult), &parsedResult)
	if err != nil {
		return AIResponseResult{}, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	return parsedResult, nil
}

// extractJSON 从文本中提取 JSON 部分
func extractJSON(text string) string {
	start := strings.Index(text, "{")
	if start < 0 {
		return text
	}

	end := strings.LastIndex(text, "}")
	if end < start {
		return text
	}

	return text[start : end+1]
}

// updateTokenUsage 更新 Token 使用统计
func updateTokenUsage(target *impl.TokenUsage, source impl.TokenUsage) {
	target.TotalTokens += source.TotalTokens
	target.PromptTokens += source.PromptTokens
	target.CompletionTokens += source.CompletionTokens
}

// retryMissingFields 重试解析缺失的字段
func (t *TaskAnalyzer) retryMissingFields(parsedTask *ParsedTask, taskInput, clientTime string) {
	// 重试解析时间
	if !parsedTask.Time.After(time.Time{}) {
		retryTime, retryTokenUsage, _ := t.parseTime(taskInput, clientTime)
		parsedTask.Time = retryTime
		parsedTask.Methods = append(parsedTask.Methods, "time")
		updateTokenUsage(&parsedTask.TokenUsage, retryTokenUsage)
	}

	// 重试解析插件
	if parsedTask.Plugin == "" {
		retryPlugin, retryTokenUsage, _ := t.parseApp(taskInput)
		parsedTask.Plugin = retryPlugin
		parsedTask.Methods = append(parsedTask.Methods, "plugin")
		updateTokenUsage(&parsedTask.TokenUsage, retryTokenUsage)
	}

	// 重试解析搜索内容
	if parsedTask.Search == "" {
		retrySearch, retryTokenUsage, _ := t.parseSearch(taskInput)
		parsedTask.Search = retrySearch
		parsedTask.Methods = append(parsedTask.Methods, "search")
		updateTokenUsage(&parsedTask.TokenUsage, retryTokenUsage)
	}

	// 重试解析任务名称
	if parsedTask.Name == "" {
		retryName, retryTokenUsage, _ := t.parseName(taskInput)
		parsedTask.Name = retryName
		parsedTask.Methods = append(parsedTask.Methods, "name")
		updateTokenUsage(&parsedTask.TokenUsage, retryTokenUsage)
	}
}

// parseTime 解析时间
func (t *TaskAnalyzer) parseTime(task, current string) (time.Time, impl.TokenUsage, error) {
	systemPrompt := config.GetPrompt("time_parser.system")
	userPrompt := config.GetPromptTemplate("time_parser.user_template", map[string]string{
		"CurrentTime": current,
		"TaskInput":   task,
	})

	req := ai.CompletionRequest{
		Messages: []ai.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Model: config.GetConfig().AI.Model,
	}
	resp, err := t.llm.Completion(context.Background(), req)
	if err != nil {
		return time.Time{}, impl.TokenUsage{}, err
	}
	parsedTime, err := time.ParseInLocation(timeutil.TimeFormat, resp.Content, time.Local)
	if err != nil {
		return time.Time{}, impl.TokenUsage{}, err
	}
	return parsedTime, impl.TokenUsage{}, nil
}

// parseApp 解析应用/插件
func (t *TaskAnalyzer) parseApp(task string) (string, impl.TokenUsage, error) {
	systemPrompt := config.GetPrompt("app_parser.system")
	userPrompt := config.GetPromptTemplate("app_parser.user_template", map[string]string{
		"TaskInput": task,
	})

	req := ai.CompletionRequest{
		Messages: []ai.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Model: config.GetConfig().AI.Model,
	}
	resp, err := t.llm.Completion(context.Background(), req)
	if err != nil {
		return "", impl.TokenUsage{}, err
	}
	result := resp.Content
	if result == "" {
		return "", impl.TokenUsage{}, fmt.Errorf("no matching plugin found for: %s", task)
	}
	return result, impl.TokenUsage{}, nil
}

// parseSearch 解析搜索内容
func (t *TaskAnalyzer) parseSearch(task string) (string, impl.TokenUsage, error) {
	systemPrompt := config.GetPrompt("search_parser.system")
	userPrompt := config.GetPromptTemplate("search_parser.user_template", map[string]string{
		"TaskInput": task,
	})

	req := ai.CompletionRequest{
		Messages: []ai.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Model: config.GetConfig().AI.Model,
	}
	resp, err := t.llm.Completion(context.Background(), req)
	if err != nil {
		return "", impl.TokenUsage{}, err
	}
	return resp.Content, impl.TokenUsage{}, nil
}

// parseName 解析任务名称
func (t *TaskAnalyzer) parseName(input string) (string, impl.TokenUsage, error) {
	systemPrompt := config.GetPrompt("name_parser.system")
	userPrompt := config.GetPromptTemplate("name_parser.user_template", map[string]string{
		"TaskInput": input,
	})

	req := ai.CompletionRequest{
		Messages: []ai.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Model: config.GetConfig().AI.Model,
	}
	resp, err := t.llm.Completion(context.Background(), req)
	if err != nil {
		return "", impl.TokenUsage{}, err
	}
	result := resp.Content
	if result == "" {
		return "", impl.TokenUsage{}, fmt.Errorf("no matching name found for: %s", input)
	}
	return result, impl.TokenUsage{}, nil
}

// ParseVoiceTask 实现语音任务解析接口（重构版：分离语音转文字和任务分析）
func (t *TaskAnalyzer) ParseVoiceTask(
	ctx context.Context,
	req *connect.Request[v1.ParseVoiceTaskRequest],
) (*connect.Response[v1.ParseVoiceTaskResponse], error) {
	startTime := time.Now()

	// 解码音频数据
	audioData, err := base64.StdEncoding.DecodeString(req.Msg.AudioData)
	if err != nil {
		logger.Error("❌ 音频数据解码失败: %v", err)
		return connect.NewResponse(&v1.ParseVoiceTaskResponse{
			Error: fmt.Sprintf("Failed to decode audio data: %v", err),
		}), nil
	}

	// 验证音频数据完整性
	if len(audioData) == 0 {
		logger.Error("❌ 音频数据为空")
		return connect.NewResponse(&v1.ParseVoiceTaskResponse{
			Error: "Audio data is empty",
		}), nil
	}

	// 确定音频MIME类型
	mimeType := t.getMimeType(req.Msg.AudioFormat)

	// 第一步：调用STT服务进行语音转文字
	sttPrompt := t.buildSTTPrompt()
	sttReq := ai.STTRequest{
		AudioData:    audioData,
		MimeType:     mimeType,
		Model:        "gemini-2.5-flash-lite-preview-06-17", // 默认模型
		LanguageCode: req.Msg.LanguageCode,
		Prompt:       sttPrompt,
	}

	sttResp, err := t.stt.TranscribeAudio(ctx, sttReq)
	if err != nil {
		logger.Error("❌ STT转录失败: %v", err)
		return connect.NewResponse(&v1.ParseVoiceTaskResponse{
			Error: fmt.Sprintf("STT transcription failed: %v", err),
		}), nil
	}

	sttDuration := time.Since(startTime)
	transcribedText := sttResp.Text

	// 显示语音识别结果
	if transcribedText != "" {
		logger.Info("🎙️ 语音识别结果: %s", transcribedText)
	} else {
		logger.Warn("⚠️ 语音识别结果为空")
	}

	// 检查转录文本是否可能被截断
	if len(transcribedText) > 0 {
		if strings.HasSuffix(transcribedText, "...") || strings.HasSuffix(transcribedText, "。。。") {
			logger.Warn("⚠️ 转录文本可能被截断 - 以省略号结尾")
		}
	}

	// 第二步：使用转录文本进行任务分析
	clientTime := req.Header().Get("bluespace-timestamp")
	clientTimezone := req.Header().Get("bluespace-timezone")

	parsedTask, err := t.parseTaskUnified(sttResp.Text, clientTime, clientTimezone)
	if err != nil {
		logger.Error("❌ 任务分析失败: %v", err)
		return connect.NewResponse(&v1.ParseVoiceTaskResponse{
			TranscribedText: sttResp.Text,
			Error:           fmt.Sprintf("Task parsing failed: %v", err),
		}), nil
	}

	totalDuration := time.Since(startTime)

	// 合并Token使用统计
	totalTokenUsage := impl.TokenUsage{
		PromptTokens:     sttResp.TokenUsage.PromptTokens + parsedTask.TokenUsage.PromptTokens,
		CompletionTokens: sttResp.TokenUsage.CompletionTokens + parsedTask.TokenUsage.CompletionTokens,
		TotalTokens:      sttResp.TokenUsage.TotalTokens + parsedTask.TokenUsage.TotalTokens,
	}

	// 记录成功的语音任务解析（仅此一条 Info 日志）
	logger.Info("✅ 语音任务解析成功 - STT耗时: %v, 任务分析耗时: %v, 总耗时: %v, 任务: '%s', 应用: '%s'",
		sttDuration, totalDuration-sttDuration, totalDuration, parsedTask.Name, parsedTask.Plugin)

	// 构建响应
	taskName := parsedTask.Name
	if taskName == "" {
		// 如果没有解析出任务名称，使用转录文本的前50个字符作为fallback
		taskName = sttResp.Text
		if len(taskName) > 50 {
			taskName = taskName[:50] + "..."
		}
		logger.Warn("⚠️ 未解析出任务名称，使用转录文本作为fallback")
	}

	response := &v1.ParseVoiceTaskResponse{
		Task: &v1.SimpleTask{
			Id:                  fmt.Sprintf("%d", time.Now().UnixMilli()),
			Name:                taskName,
			Search:              parsedTask.Search,
			StartTime:           timestamppb.New(parsedTask.Time),
			Repeatable:          parsedTask.Repeatable,
			RepeatTimes:         parsedTask.RepeatTimes,
			RepeatIntervalValue: parsedTask.RepeatIntervalValue,
			RepeatIntervalUnit:  stringToRepeatIntervalUnit(parsedTask.RepeatIntervalUnit),
			Description:         sttResp.Text,
			Plugin:              parsedTask.Plugin,
			Timezone:            req.Header().Get("bluespace-timezone"),
		},
		TranscribedText: sttResp.Text,
		TokenUsage: &v1.TokenUsage{
			PromptTokens:     int32(totalTokenUsage.PromptTokens),
			CompletionTokens: int32(totalTokenUsage.CompletionTokens),
			TotalTokens:      int32(totalTokenUsage.TotalTokens),
		},
		CompletedAt:   timestamppb.New(time.Now()),
		ModelUsed:     sttReq.Model,
		AudioDuration: float32(totalDuration.Seconds()),
	}

	return connect.NewResponse(response), nil
}

// parseVoiceTaskJSON 解析语音任务返回的JSON结果
// 注意：此函数已废弃，现在使用 STT + parseTaskUnified 的方式

// getMimeType 根据音频格式获取MIME类型
func (t *TaskAnalyzer) getMimeType(format v1.AudioFormat) string {
	switch format {
	case v1.AudioFormat_MP3:
		return "audio/mpeg"
	case v1.AudioFormat_WAV:
		return "audio/wav"
	case v1.AudioFormat_M4A:
		return "audio/m4a"
	case v1.AudioFormat_OGG:
		return "audio/ogg"
	case v1.AudioFormat_FLAC:
		return "audio/flac"
	case v1.AudioFormat_PCM:
		return "audio/pcm"
	default:
		return "audio/mpeg" // 默认
	}
}

// buildSTTPrompt 构建语音转文字的提示词（专门用于STT）
func (t *TaskAnalyzer) buildSTTPrompt() string {
	// 获取系统提示词
	systemPrompt := config.GetPrompt("stt_transcriber.system")

	// 获取用户提示词模板（无需参数）
	userPrompt := config.GetPromptTemplate("stt_transcriber.user_template", map[string]string{})

	// 合并系统提示词和用户提示词
	fullPrompt := systemPrompt + "\n\n" + userPrompt

	return fullPrompt
}

// buildVoiceTaskPrompt 构建语音任务解析的提示词（使用配置文件中的模板）
// 注意：此函数已废弃，现在使用 buildSTTPrompt + parseTaskUnified 的方式
func (t *TaskAnalyzer) buildVoiceTaskPrompt(installedApps []string) string {
	now := time.Now()
	timestamp := now.Unix()
	timeString := now.Format("2006-01-02 15:04:05")
	timezone := now.Location().String()

	// 准备模板参数
	templateParams := map[string]string{
		"CurrentTime":       fmt.Sprintf("%d", timestamp),
		"CurrentTimeString": timeString,
		"CurrentTimezone":   timezone,
		"TaskInput":         "用户的语音输入", // 占位符，STT服务会自动处理实际的语音转录文本
	}

	// 获取系统提示词
	systemPrompt := config.GetPrompt("voice_task_analyzer.system")

	// 获取用户提示词模板
	userPrompt := config.GetPromptTemplate("voice_task_analyzer.user_template", templateParams)

	// 合并系统提示词和用户提示词
	fullPrompt := systemPrompt + "\n\n" + userPrompt

	return fullPrompt
}

// parseTaskFromText 从转录文本中解析任务信息
// 注意：此函数已废弃，现在语音任务解析直接使用 parseVoiceTaskJSON
// 保留此函数仅用于文本任务分析（Analyze接口）
func (t *TaskAnalyzer) parseTaskFromText(text, clientTime, clientTimezone string) (ParsedTask, error) {
	return t.parseTaskUnified(text, clientTime, clientTimezone)
}
