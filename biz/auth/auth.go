package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"fastserver.com/fastserver/config"
	"fastserver.com/fastserver/utils/logger"
)

type AuthCache struct {
	sync.RWMutex
	cache map[string]*CacheEntry
}

type CacheEntry struct {
	UserID    string
	ExpiresAt time.Time
}

var (
	authCache = &AuthCache{
		cache: make(map[string]*CacheEntry),
	}

	excludePathsMutex sync.RWMutex
	excludePaths      = make(map[string]bool)
)

// InitExcludePaths 初始化不需要认证的路径
func InitExcludePaths() {
	excludePathsMutex.Lock()
	defer excludePathsMutex.Unlock()

	// 从配置中加载
	cfg := config.GetConfig()
	for _, path := range cfg.Auth.ExcludePaths {
		excludePaths[path] = true
		logger.Info("Auth excluded path from config: %s", path)
	}
}

// IsPathExcluded 检查路径是否在排除列表中
func IsPathExcluded(path string) bool {
	excludePathsMutex.RLock()
	defer excludePathsMutex.RUnlock()
	return excludePaths[path]
}

// ValidateToken 验证访问令牌，如果有效则返回用户ID
func ValidateToken(ctx context.Context, accessToken string) (string, error) {
	// 首先检查缓存
	if entry := authCache.get(accessToken); entry != nil {
		return entry.UserID, nil
	}

	// 缓存未命中，调用 Supabase Auth
	userID, err := validateWithSupabase(ctx, accessToken)
	if err != nil {
		return "", err
	}

	// 将结果存入缓存
	authCache.set(accessToken, userID)
	return userID, nil
}

func (ac *AuthCache) get(token string) *CacheEntry {
	ac.RLock()
	defer ac.RUnlock()

	if entry, exists := ac.cache[token]; exists {
		if time.Now().Before(entry.ExpiresAt) {
			return entry
		}
		// 如果已过期，删除缓存
		delete(ac.cache, token)
	}
	return nil
}

func (ac *AuthCache) set(token, userID string) {
	ac.Lock()
	defer ac.Unlock()

	cfg := config.GetConfig()
	ac.cache[token] = &CacheEntry{
		UserID:    userID,
		ExpiresAt: time.Now().Add(cfg.Auth.Cache.Duration()),
	}
}

func validateWithSupabase(ctx context.Context, accessToken string) (string, error) {
	cfg := config.GetConfig()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", cfg.Auth.Supabase.URL+"/auth/v1/user", nil)
	if err != nil {
		return "", fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("apikey", cfg.Auth.Supabase.Key)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("invalid token, status: %d", resp.StatusCode)
	}

	var result struct {
		ID string `json:"id"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("decode response failed: %w", err)
	}

	return result.ID, nil
}
