package update

import (
	"context"
	"fmt"

	"connectrpc.com/connect"
	v1 "fastserver.com/fastserver/gen/proto/update/v1"
	"fastserver.com/fastserver/utils/logger"
)

// UpdateHandler 实现更新服务接口
type UpdateHandler struct {
	releaseClient *ReleaseClient
}

// NewUpdateHandler 创建一个新的更新处理程序
func NewUpdateHandler() (*UpdateHandler, error) {
	releaseClient, err := NewReleaseClient()
	if err != nil {
		return nil, fmt.Errorf("failed to create release client: %w", err)
	}

	return &UpdateHandler{
		releaseClient: releaseClient,
	}, nil
}

// CheckUpdate 检查是否有新版本可用
func (h *UpdateHandler) CheckUpdate(
	ctx context.Context,
	req *connect.Request[v1.CheckUpdateRequest],
) (*connect.Response[v1.CheckUpdateResponse], error) {
	// 从请求头获取当前版本
	currentVersionStr := req.Header().Get("app-version")
	if currentVersionStr == "" {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("missing app-version header"))
	}

	logger.Info("Received CheckUpdate request with version: %s", currentVersionStr)

	// 解析当前版本
	currentVersion, err := ParseVersion(currentVersionStr)
	if err != nil {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("invalid version format: %w", err))
	}

	// 获取最新版本信息
	latestRelease, err := h.releaseClient.GetLatestRelease(ctx)
	if err != nil {
		logger.Error("Failed to get latest release: %v", err)
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("failed to check for updates: %w", err))
	}

	// 比较版本
	hasUpdate := latestRelease.Version.IsNewer(currentVersion)

	// 构建响应
	response := &v1.CheckUpdateResponse{
		HasUpdate:      hasUpdate,
		ForceUpdate:    latestRelease.ForceUpdate,
		CurrentVersion: currentVersion.String(),
		LatestVersion:  latestRelease.Version.String(),
		ReleaseNote:    latestRelease.ReleaseNote,
	}

	logger.Info("CheckUpdate result: hasUpdate=%v, forceUpdate=%v, currentVersion=%s, latestVersion=%s",
		response.HasUpdate, response.ForceUpdate, response.CurrentVersion, response.LatestVersion)

	return connect.NewResponse(response), nil
}
