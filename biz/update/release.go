package update

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"

	"fastserver.com/fastserver/config"
	"fastserver.com/fastserver/utils/logger"
)

// ReleaseInfo 存储发布信息
type ReleaseInfo struct {
	Version     *Version
	ReleaseNote string
	ForceUpdate bool
}

// AppRelease 表示数据库中的应用发布记录
type AppRelease struct {
	ID          int64  `json:"id"`
	Version     string `json:"version"`
	Released    bool   `json:"released"`
	ForceUpdate bool   `json:"force_update"`
	ReleaseNote string `json:"release_note"`
}

// ReleaseClient 是与 Supabase 数据库交互的客户端
type ReleaseClient struct {
	supabaseURL string
	apiKey      string
	httpClient  *http.Client
}

// NewReleaseClient 创建一个新的 Supabase 数据库客户端
func NewReleaseClient() (*ReleaseClient, error) {
	cfg := config.GetConfig()

	// 从配置中获取 Supabase URL 和 API 密钥
	supabaseURL := cfg.Auth.Supabase.URL
	apiKey := cfg.Auth.Supabase.Key

	if supabaseURL == "" {
		return nil, fmt.Errorf("missing Supabase URL in config")
	}

	if apiKey == "" {
		return nil, fmt.Errorf("missing Supabase API key in config")
	}

	// 确保 URL 以 / 结尾
	if !strings.HasSuffix(supabaseURL, "/") {
		supabaseURL += "/"
	}

	return &ReleaseClient{
		supabaseURL: supabaseURL,
		apiKey:      apiKey,
		httpClient:  &http.Client{Timeout: 10 * time.Second},
	}, nil
}

// ListReleases 列出所有已发布的版本
func (c *ReleaseClient) ListReleases(ctx context.Context) ([]AppRelease, error) {
	// 构建 API URL
	apiURL := fmt.Sprintf("%srest/v1/app_releases?released=eq.true&select=*", c.supabaseURL)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("apikey", c.apiKey)
	req.Header.Set("Authorization", "Bearer "+c.apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list releases: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var releases []AppRelease
	if err := json.Unmarshal(body, &releases); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return releases, nil
}

// GetRelease 获取指定版本的发布信息
func (c *ReleaseClient) GetRelease(ctx context.Context, version string) (*AppRelease, error) {
	// 构建 API URL
	apiURL := fmt.Sprintf("%srest/v1/app_releases?version=eq.%s&released=eq.true&select=*", c.supabaseURL, version)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("apikey", c.apiKey)
	req.Header.Set("Authorization", "Bearer "+c.apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get release: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d", resp.StatusCode)
	}

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 解析响应
	var releases []AppRelease
	if err := json.Unmarshal(body, &releases); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(releases) == 0 {
		return nil, fmt.Errorf("release with version %s not found", version)
	}

	return &releases[0], nil
}

// GetLatestRelease 获取最新的发布信息
func (c *ReleaseClient) GetLatestRelease(ctx context.Context) (*ReleaseInfo, error) {
	// 获取所有已发布的版本
	releases, err := c.ListReleases(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list releases: %w", err)
	}

	if len(releases) == 0 {
		return nil, fmt.Errorf("no releases found")
	}

	// 解析版本号并排序
	versions := make([]*Version, 0, len(releases))
	releaseMap := make(map[string]AppRelease) // 版本号字符串到发布信息的映射

	for _, release := range releases {
		version, err := ParseVersion(release.Version)
		if err != nil {
			logger.Warn("Skipping invalid version: %s, error: %v", release.Version, err)
			continue
		}

		versions = append(versions, version)
		releaseMap[version.String()] = release
	}

	if len(versions) == 0 {
		return nil, fmt.Errorf("no valid version releases found")
	}

	// 按版本号排序
	sort.Slice(versions, func(i, j int) bool {
		isNewer := versions[i].IsNewer(versions[j])
		return isNewer // 降序排列，最新的在前面
	})

	// 获取最新版本的发布信息
	latestVersion := versions[0]
	latestRelease := releaseMap[latestVersion.String()]

	return &ReleaseInfo{
		Version:     latestVersion,
		ReleaseNote: latestRelease.ReleaseNote,
		ForceUpdate: latestRelease.ForceUpdate,
	}, nil
}
