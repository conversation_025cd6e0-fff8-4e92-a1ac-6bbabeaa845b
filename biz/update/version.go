package update

import (
	"fmt"
	"strconv"
	"strings"
)

// Version 表示语义化版本号
type Version struct {
	Major int
	Minor int
	Patch int
}

// ParseVersion 从字符串解析版本号
func ParseVersion(version string) (*Version, error) {
	// 移除可能的前缀 'v'
	version = strings.TrimPrefix(version, "v")

	parts := strings.Split(version, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid version format: %s", version)
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return nil, fmt.Errorf("invalid major version: %s", parts[0])
	}

	minor, err := strconv.Atoi(parts[1])
	if err != nil {
		return nil, fmt.Errorf("invalid minor version: %s", parts[1])
	}

	patch, err := strconv.Atoi(parts[2])
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("invalid patch version: %s", parts[2])
	}

	return &Version{
		Major: major,
		Minor: minor,
		Patch: patch,
	}, nil
}

// String 返回版本号的字符串表示
func (v *Version) String() string {
	return fmt.Sprintf("%d.%d.%d", v.Major, v.Minor, v.Patch)
}

// IsNewer 判断当前版本是否比另一个版本更新
func (v *Version) IsNewer(other *Version) bool {
	if v.Major > other.Major {
		return true
	}
	if v.Major < other.Major {
		return false
	}

	// Major版本相同，比较Minor
	if v.Minor > other.Minor {
		return true
	}
	if v.Minor < other.Minor {
		return false
	}

	// Minor版本也相同，比较Patch
	return v.Patch > other.Patch
}

// IsMajorUpdate 判断是否为主要版本更新（Major版本不同）
func (v *Version) IsMajorUpdate(other *Version) bool {
	return v.Major != other.Major
}
