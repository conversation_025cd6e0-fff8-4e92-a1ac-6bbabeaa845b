package update

import (
	"context"
	"fmt"
	"time"

	v1 "fastserver.com/fastserver/gen/proto/update/v1"
	"fastserver.com/fastserver/utils/logger"
)

// UpdateService 是更新服务的主要实现
type UpdateService struct {
	releaseClient *ReleaseClient
	cache         *ReleaseCache
}

// ReleaseCache 缓存最新的发布信息
type ReleaseCache struct {
	LatestRelease *ReleaseInfo
	LastUpdated   time.Time
	TTL           time.Duration
}

// NewUpdateService 创建一个新的更新服务
func NewUpdateService() (*UpdateService, error) {
	releaseClient, err := NewReleaseClient()
	if err != nil {
		return nil, fmt.Errorf("failed to create release client: %w", err)
	}

	return &UpdateService{
		releaseClient: releaseClient,
		cache: &ReleaseCache{
			TTL: 15 * time.Minute, // 缓存15分钟
		},
	}, nil
}

// GetLatestRelease 获取最新的发布信息，带缓存
func (s *UpdateService) GetLatestRelease(ctx context.Context) (*ReleaseInfo, error) {
	// 检查缓存是否有效
	if s.cache.LatestRelease != nil && time.Since(s.cache.LastUpdated) < s.cache.TTL {
		logger.Debug("Using cached release info")
		return s.cache.LatestRelease, nil
	}

	// 缓存无效，从数据库获取最新信息
	logger.Debug("Fetching latest release info from database")
	release, err := s.releaseClient.GetLatestRelease(ctx)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	s.cache.LatestRelease = release
	s.cache.LastUpdated = time.Now()

	return release, nil
}

// CheckForUpdates 检查指定版本是否需要更新
func (s *UpdateService) CheckForUpdates(ctx context.Context, currentVersionStr string) (*v1.CheckUpdateResponse, error) {
	// 解析当前版本
	currentVersion, err := ParseVersion(currentVersionStr)
	if err != nil {
		return nil, fmt.Errorf("invalid version format: %w", err)
	}

	// 获取最新版本信息
	latestRelease, err := s.GetLatestRelease(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest release: %w", err)
	}

	// 比较版本
	hasUpdate := latestRelease.Version.IsNewer(currentVersion)

	// 构建响应
	return &v1.CheckUpdateResponse{
		HasUpdate:      hasUpdate,
		ForceUpdate:    latestRelease.ForceUpdate,
		CurrentVersion: currentVersion.String(),
		LatestVersion:  latestRelease.Version.String(),
		ReleaseNote:    latestRelease.ReleaseNote,
	}, nil
}
