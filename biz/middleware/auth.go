package middleware

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strings"

	"connectrpc.com/connect"
	"fastserver.com/fastserver/biz/auth"
)

type AuthMiddleware struct {
	// 不再需要在结构体中存储排除路径
}

func NewAuthMiddleware() *AuthMiddleware {
	// 初始化排除路径
	auth.InitExcludePaths()
	return &AuthMiddleware{}
}

type userIDKey struct{}

// writeAuthError 处理认证错误响应
func writeAuthError(w http.ResponseWriter, code connect.Code, msg string) {
	connectErr := connect.NewError(code, errors.New(msg))
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusUnauthorized)
	json.NewEncoder(w).Encode(map[string]string{
		"code":    code.String(),
		"message": connectErr.Error(),
	})
}

// WrapHandler wraps a connect-go handler with authentication middleware
func (m *AuthMiddleware) WrapHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查是否是排除的路径
		if auth.IsPathExcluded(r.URL.Path) {
			next.ServeHTTP(w, r)
			return
		}

		// 尝试从不同的头部获取 token
		var token string

		// 1. 尝试从 Authorization 头部获取 Bearer token
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			token = strings.TrimPrefix(authHeader, "Bearer ")
		}

		// 2. 如果没有找到 Bearer token，尝试从 token 头部字段获取
		if token == "" {
			token = r.Header.Get("token")
		}

		// 如果两种方式都没有找到 token
		if token == "" {
			writeAuthError(w, connect.CodeUnauthenticated, "missing token in authorization header or token header")
			return
		}

		// 验证 token
		userID, err := auth.ValidateToken(r.Context(), token)
		if err != nil {
			writeAuthError(w, connect.CodeUnauthenticated, "invalid token")
			return
		}

		// 将用户ID添加到上下文
		ctx := context.WithValue(r.Context(), userIDKey{}, userID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetUserID 从上下文中获取用户ID
func GetUserID(ctx context.Context) (string, bool) {
	userID, ok := ctx.Value(userIDKey{}).(string)
	return userID, ok
}
