package middleware

import (
	"encoding/json"
	"net/http"
)

// ErrorResponse 统一的错误响应结构
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// WriteError 写入错误响应
func WriteError(w http.ResponseWriter, statusCode int, message string) {
	resp := ErrorResponse{
		Code:    statusCode,
		Message: message,
	}

	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	_ = json.NewEncoder(w).Encode(resp)
}
