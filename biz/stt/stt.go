package stt

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"connectrpc.com/connect"
	"fastserver.com/fastserver/ai"
	sttv1 "fastserver.com/fastserver/gen/proto/stt/v1"
	taskv1 "fastserver.com/fastserver/gen/proto/task/v1"
	"fastserver.com/fastserver/utils/logger"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// STTService STT 服务实现
type STTService struct {
	sttProvider ai.STTProvider
}

// NewSTTService 创建新的 STT 服务
func NewSTTService(sttProvider ai.STTProvider) *STTService {
	return &STTService{
		sttProvider: sttProvider,
	}
}

// TranscribeAudio 实现语音转文本接口
func (s *STTService) TranscribeAudio(
	ctx context.Context,
	req *connect.Request[sttv1.TranscribeRequest],
) (*connect.Response[sttv1.TranscribeResponse], error) {
	logger.Info("Received STT transcription request")

	// 验证请求参数
	if req.Msg.AudioData == "" {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("audio data is required"))
	}

	// 解码 base64 音频数据
	audioData, err := base64.StdEncoding.DecodeString(req.Msg.AudioData)
	if err != nil {
		logger.Error("Failed to decode audio data: %v", err)
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("invalid base64 audio data: %w", err))
	}

	// 确定 MIME 类型
	mimeType := s.getMimeTypeFromFormat(req.Msg.AudioFormat)
	if mimeType == "" {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("unsupported audio format: %v", req.Msg.AudioFormat))
	}

	// 构建 STT 请求
	prompt := s.buildPrompt(req.Msg)
	logger.Info("Generated prompt for STT: %s", prompt)
	logger.Info("Installed apps: %v", req.Msg.InstalledApps)

	sttReq := ai.STTRequest{
		AudioData:    audioData,
		MimeType:     mimeType,
		Model:        req.Msg.Model,
		LanguageCode: req.Msg.LanguageCode,
		Prompt:       prompt,
	}

	// 调用 STT 提供商
	startTime := time.Now()
	sttResp, err := s.sttProvider.TranscribeAudio(ctx, sttReq)
	if err != nil {
		logger.Error("STT transcription failed: %v", err)
		return &connect.Response[sttv1.TranscribeResponse]{
			Msg: &sttv1.TranscribeResponse{
				Error:       fmt.Sprintf("Transcription failed: %v", err),
				CompletedAt: timestamppb.New(time.Now()),
				ModelUsed:   sttReq.Model,
			},
		}, nil
	}
	duration := time.Since(startTime)

	logger.Info("STT transcription completed in %v", duration)

	// 构建响应
	response := &sttv1.TranscribeResponse{
		Text: sttResp.Text,
		TokenUsage: &taskv1.TokenUsage{
			PromptTokens:     int32(sttResp.TokenUsage.PromptTokens),
			CompletionTokens: int32(sttResp.TokenUsage.CompletionTokens),
			TotalTokens:      int32(sttResp.TokenUsage.TotalTokens),
		},
		CompletedAt:   timestamppb.New(time.Now()),
		ModelUsed:     sttReq.Model,
		AudioDuration: float32(sttResp.AudioDuration),
	}

	// 如果启用了任务分析，尝试解析任务信息
	if req.Msg.EnableTaskAnalysis && sttResp.Text != "" {
		taskInfo, err := s.parseTaskInfo(sttResp.Text)
		if err != nil {
			logger.Warn("Failed to parse task info: %v", err)
		} else {
			response.TaskInfo = taskInfo
		}
	}

	return &connect.Response[sttv1.TranscribeResponse]{
		Msg: response,
	}, nil
}

// getMimeTypeFromFormat 根据音频格式获取 MIME 类型
func (s *STTService) getMimeTypeFromFormat(format sttv1.AudioFormat) string {
	switch format {
	case sttv1.AudioFormat_MP3:
		return "audio/mp3"
	case sttv1.AudioFormat_WAV:
		return "audio/wav"
	case sttv1.AudioFormat_M4A:
		return "audio/m4a"
	case sttv1.AudioFormat_OGG:
		return "audio/ogg"
	case sttv1.AudioFormat_FLAC:
		return "audio/flac"
	case sttv1.AudioFormat_PCM:
		return "audio/pcm"
	default:
		return ""
	}
}

// buildPrompt 构建提示词
func (s *STTService) buildPrompt(req *sttv1.TranscribeRequest) string {
	if req.Prompt != "" {
		return req.Prompt
	}

	if req.EnableTaskAnalysis {
		return s.getTaskAnalysisPrompt(req.InstalledApps)
	}

	return "请将音频内容转录为文本。"
}

// getTaskAnalysisPrompt 获取任务分析提示词（基于 TtsDemo 项目）
func (s *STTService) getTaskAnalysisPrompt(installedApps []string) string {
	now := time.Now()
	timestamp := now.Unix()
	timeString := now.Format("2006-01-02 15:04:05")
	timezone := now.Location().String()

	// 构建已安装应用的提示文本
	var appHint string
	if len(installedApps) > 0 {
		appHint = fmt.Sprintf(`

用户已安装的应用列表: %s
注意：在分析任务时，请优先从用户已安装的应用中选择匹配的应用名称。如果用户提到的应用名称与已安装列表中的某个应用相似（包括别名、同音字等），请使用已安装列表中的准确名称。`, strings.Join(installedApps, ", "))
	}

	prompt := fmt.Sprintf(`你是一个专业的任务分析AI。你的目标是分析用户的语音指令，并将关键信息提取为结构化的JSON格式。

当前时间戳: %d
当前时间: %s
当前时区: %s%s

请分析用户语音指令中的信息，并仅返回一个合法的、可解析的JSON对象。不要包含任何其他文本、解释或Markdown格式。

JSON输出必须严格遵循以下结构:
{
  "time": "YYYY-MM-DD HH:MM:SS",
  "plugin": "应用名称",
  "search": "搜索内容",
  "name": "任务简洁标题",
  "repeatable": false,
  "repeat_times": 0,
  "repeat_interval_value": 0,
  "repeat_interval_unit": ""
}

请严格遵守以下规则:
1. time: 任务的执行时间，格式为 "YYYY-MM-DD HH:MM:SS"。如果只提到时间点（如 "六点"），默认为当天；若时间已过则为明天。对于周几，使用即将到来的日期。
2. plugin: 应用名称，如果用户已安装应用列表中有匹配项，请优先使用列表中的准确名称。
3. repeatable: 仅当用户明确提到重复（如 "每天"、"每周"）时才为 true。
4. repeat_times: 对于无限次重复，请使用 2147483647。对于不重复的任务，则为 0。
5. repeat_interval_unit: 单位可以是 "minute", "hour", "day", "week", "month", "year"。

现在，请分析音频中的用户指令。`, timestamp, timeString, timezone, appHint)

	return prompt
}

// parseTaskInfo 解析任务信息
func (s *STTService) parseTaskInfo(text string) (*taskv1.SimpleTask, error) {
	// 尝试从文本中解析 JSON
	text = strings.TrimSpace(text)

	// 查找 JSON 开始和结束位置
	start := strings.Index(text, "{")
	end := strings.LastIndex(text, "}")

	if start == -1 || end == -1 || start >= end {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonStr := text[start : end+1]

	// 定义临时结构体用于解析
	var taskData struct {
		Time                string `json:"time"`
		Plugin              string `json:"plugin"`
		Search              string `json:"search"`
		Name                string `json:"name"`
		Repeatable          bool   `json:"repeatable"`
		RepeatTimes         int32  `json:"repeat_times"`
		RepeatIntervalValue int32  `json:"repeat_interval_value"`
		RepeatIntervalUnit  string `json:"repeat_interval_unit"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &taskData); err != nil {
		return nil, fmt.Errorf("failed to parse task JSON: %w", err)
	}

	// 解析时间字符串为 Timestamp
	parsedTime, err := time.Parse("2006-01-02 15:04:05", taskData.Time)
	if err != nil {
		return nil, fmt.Errorf("failed to parse time: %w", err)
	}

	// 映射重复间隔单位
	var repeatUnit taskv1.RepeatIntervalUnit
	switch taskData.RepeatIntervalUnit {
	case "minute":
		repeatUnit = taskv1.RepeatIntervalUnit_REPEAT_INTERVAL_UNIT_UNSPECIFIED // 暂时使用默认值，因为没有 minute
	case "hour":
		repeatUnit = taskv1.RepeatIntervalUnit_REPEAT_INTERVAL_UNIT_UNSPECIFIED // 暂时使用默认值，因为没有 hour
	case "day":
		repeatUnit = taskv1.RepeatIntervalUnit_DAY
	case "week":
		repeatUnit = taskv1.RepeatIntervalUnit_WEEK
	case "month":
		repeatUnit = taskv1.RepeatIntervalUnit_MONTH
	case "year":
		repeatUnit = taskv1.RepeatIntervalUnit_YEAR
	default:
		repeatUnit = taskv1.RepeatIntervalUnit_REPEAT_INTERVAL_UNIT_UNSPECIFIED
	}

	return &taskv1.SimpleTask{
		Id:                  "", // 将由调用方设置
		Name:                taskData.Name,
		Plugin:              taskData.Plugin,
		Search:              taskData.Search,
		StartTime:           timestamppb.New(parsedTime),
		Repeatable:          taskData.Repeatable,
		RepeatTimes:         taskData.RepeatTimes,
		RepeatIntervalValue: taskData.RepeatIntervalValue,
		RepeatIntervalUnit:  repeatUnit,
		Description:         taskData.Name, // 使用名称作为描述
		Timezone:            time.Now().Location().String(),
	}, nil
}
