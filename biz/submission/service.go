package submission

import (
	"context"
	"fmt"

	"connectrpc.com/connect"
	"fastserver.com/fastserver/biz/store"
	submissionv1 "fastserver.com/fastserver/gen/proto/submission/v1"
	submissionv1connect "fastserver.com/fastserver/gen/proto/submission/v1/submissionv1connect"
	"fastserver.com/fastserver/utils/logger"
	"google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

// Helper to get a pointer to a string
func stringPtr(s string) *string {
	if s == "" {
		return nil // Consider if empty string should be nil or *""
	}
	return &s
}

// Helper to get a pointer to a bool
func boolPtr(b bool) *bool {
	return &b
}

// Helper to dereference a string pointer, returning a default value if nil
func stringDeref(s *string, def string) string {
	if s == nil {
		return def
	}
	return *s
}

// Ensure Service implements the generated interface using the correct package
var _ submissionv1connect.SubmissionServiceHandler = (*Service)(nil)

// Service handles submission logic.
type Service struct {
	dbClient *store.DBClient
}

// NewSubmissionService creates a new submission service.
func NewSubmissionService(dbClient *store.DBClient) *Service {
	if dbClient == nil {
		logger.Error("DBClient cannot be nil for SubmissionService")
		return nil // Indicate failure if logger.Panic is not available/desired
	}
	return &Service{
		dbClient: dbClient,
	}
}

// SubmitDeeplink handles the RPC call to submit a new deeplink.
func (s *Service) SubmitDeeplink(ctx context.Context, req *connect.Request[submissionv1.SubmitDeeplinkRequest]) (*connect.Response[submissionv1.SubmitDeeplinkResponse], error) {
	// 1. Get User ID from context (provided by auth middleware)
	// REMOVED: This endpoint is unauthenticated, so we don't expect a User ID.
	/*
		userIDStr, ok := middleware.GetUserID(ctx)
		if !ok {
			logger.Warn("User ID not found in context for SubmitDeeplink")
			return nil, connect.NewError(connect.CodeUnauthenticated, fmt.Errorf("user not authenticated"))
		}

		userID, err := uuid.Parse(userIDStr)
		if err != nil {
			logger.Error("Failed to parse user ID '%s' from context: %v", userIDStr, err)
			return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("internal server error processing user ID"))
		}
	*/

	// 2. Validate request
	msg := req.Msg
	if msg.Deeplink == "" || msg.Name == "" || msg.Type == "" || msg.AppPackageName == "" {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("missing required fields: deeplink, name, type, app_package_name"))
	}

	// 3. Construct store model
	submitted := store.SubmittedDeeplink{
		// UserID:         &userID, // REMOVED: Set to nil as it's unauthenticated
		UserID:         nil,
		Deeplink:       stringPtr(msg.Deeplink),
		Name:           stringPtr(msg.Name),
		Type:           stringPtr(msg.Type),
		AppPackageName: stringPtr(msg.AppPackageName),
		// Status defaults to 'unhandled' in the database
		// CreatedAt/UpdatedAt handled by DB
	}

	// Handle proto optional fields
	submitted.RequiresTextInput = boolPtr(msg.GetRequiresTextInput()) // Returns default (false) if not set
	if msg.OpenWithApp != nil {                                       // Check presence for optional string
		submitted.OpenWithApp = msg.OpenWithApp
	}
	if msg.OpenWithActivity != nil { // Check presence for optional string
		submitted.OpenWithActivity = msg.OpenWithActivity
	}

	// 4. Call store layer to insert
	inserted, err := s.dbClient.InsertSubmittedDeeplink(ctx, submitted)
	if err != nil {
		logger.Error("Failed to insert submitted deeplink into store: %v", err)
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("failed to save submission"))
	}

	// 5. Construct and return response
	respProto := &submissionv1.SubmittedDeeplink{
		Id: inserted.ID,
		// UserId:        inserted.UserID.String(), // Check if UserID is nil before calling String()
		Deeplink:       wrapperspb.String(stringDeref(inserted.Deeplink, "")),
		Name:           wrapperspb.String(stringDeref(inserted.Name, "")),
		Type:           wrapperspb.String(stringDeref(inserted.Type, "")),
		AppPackageName: wrapperspb.String(stringDeref(inserted.AppPackageName, "")),
		Status:         wrapperspb.String(stringDeref(inserted.Status, "unhandled")), // Provide default if needed
		CreatedAt:      timestamppb.New(inserted.CreatedAt),
		UpdatedAt:      timestamppb.New(inserted.UpdatedAt),
	}
	// Handle potentially nil UserID in the response
	if inserted.UserID != nil {
		respProto.UserId = inserted.UserID.String()
	} // Otherwise, UserId remains empty string (default for proto string)

	if inserted.RequiresTextInput != nil {
		respProto.RequiresTextInput = wrapperspb.Bool(*inserted.RequiresTextInput)
	}
	if inserted.OpenWithApp != nil {
		respProto.OpenWithApp = wrapperspb.String(*inserted.OpenWithApp)
	}
	if inserted.OpenWithActivity != nil {
		respProto.OpenWithActivity = wrapperspb.String(*inserted.OpenWithActivity)
	}

	res := connect.NewResponse(&submissionv1.SubmitDeeplinkResponse{
		SubmittedDeeplink: respProto,
	})

	return res, nil
}
