package store

import (
	"context"
	"fmt"
	"strings"
	"time"

	"fastserver.com/fastserver/config"
	"fastserver.com/fastserver/utils/logger"
	"github.com/google/uuid"
	"github.com/supabase-community/postgrest-go"
	"github.com/supabase-community/supabase-go"
)

// Helper function to check if the language requires English tables
func isEnglish(language string) bool {
	return language != "zh"
}

// Helper function to get the correct preset_groups table name
func getPresetGroupsTableName(language string) string {
	if isEnglish(language) {
		return "preset_groups_en"
	}
	return "preset_groups"
}

// Helper function to get the correct deeplink_preset_groups table name
func getDeeplinkPresetGroupsTableName(language string) string {
	if isEnglish(language) {
		return "deeplink_preset_groups_en"
	}
	return "deeplink_preset_groups"
}

// DBClient 是与 Supabase 交互的客户端
type DBClient struct {
	client             *supabase.Client
	pluginCountCache   int32
	pluginCountCacheAt time.Time
}

// NewDBClient 创建一个新的 Supabase 客户端
func NewDBClient() (*DBClient, error) {
	cfg := config.GetConfig()

	// 从配置中获取 Supabase URL 和 API 密钥
	supabaseURL := cfg.Auth.Supabase.URL
	apiKey := cfg.Auth.Supabase.Key

	if supabaseURL == "" {
		return nil, fmt.Errorf("missing Supabase URL in config")
	}

	if apiKey == "" {
		return nil, fmt.Errorf("missing Supabase API key in config")
	}

	// 确保 URL 不以 / 结尾，因为 supabase-go 客户端会自动添加
	supabaseURL = strings.TrimSuffix(supabaseURL, "/")

	// 创建 Supabase 客户端
	client, err := supabase.NewClient(supabaseURL, apiKey, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Supabase client: %w", err)
	}

	return &DBClient{
		client: client,
	}, nil
}

// Close 关闭客户端
func (c *DBClient) Close() {
	// Supabase Go 客户端不需要显式关闭
}

// GetAppByID 通过 ID 获取应用
func (c *DBClient) GetAppByID(ctx context.Context, id string) (*App, error) {
	var apps []App
	_, err := c.client.From("apps").
		Select("*", "", false).
		Eq("id", id).
		ExecuteTo(&apps)
	if err != nil {
		return nil, fmt.Errorf("failed to get app: %w", err)
	}

	if len(apps) == 0 {
		return nil, fmt.Errorf("app not found")
	}

	return &apps[0], nil
}

// GetAppCategories 获取应用的所有类别
func (c *DBClient) GetAppCategories(ctx context.Context, appID string) ([]string, error) {
	type CategoryResult struct {
		Categories struct {
			Name string `json:"name"`
		} `json:"categories"`
	}

	var results []CategoryResult
	_, err := c.client.From("app_categories").
		Select("categories(name)", "", false).
		Eq("app_id", appID).
		ExecuteTo(&results)
	if err != nil {
		return nil, fmt.Errorf("failed to get app categories: %w", err)
	}

	// 提取类别名称
	categories := make([]string, 0, len(results))
	for _, item := range results {
		categories = append(categories, item.Categories.Name)
	}

	return categories, nil
}

// GetAppDeeplinks 获取应用的所有深度链接
func (c *DBClient) GetAppDeeplinks(ctx context.Context, appID string) ([]Deeplink, error) {
	var deeplinks []Deeplink
	_, err := c.client.From("deeplinks").
		Select("*", "", false).
		Eq("app_id", appID).
		Order("name", &postgrest.OrderOpts{Ascending: true}).
		ExecuteTo(&deeplinks)
	if err != nil {
		return nil, fmt.Errorf("failed to get app deeplinks: %w", err)
	}

	return deeplinks, nil
}

// GetCategories 获取所有类别
func (c *DBClient) GetCategories(ctx context.Context) ([]string, error) {
	type CategoryResult struct {
		Name string `json:"name"`
	}

	var results []CategoryResult
	_, err := c.client.From("categories").
		Select("name", "", false).
		Order("name", &postgrest.OrderOpts{Ascending: true}).
		ExecuteTo(&results)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	// 提取类别名称
	categories := make([]string, 0, len(results))
	for _, item := range results {
		categories = append(categories, item.Name)
	}

	return categories, nil
}

// GetPresetGroups 获取所有预设组 (language specific)
func (c *DBClient) GetPresetGroups(ctx context.Context, language string) ([]string, error) {
	type PresetGroupResult struct {
		Name string `json:"name"`
	}

	tableName := getPresetGroupsTableName(language)
	var results []PresetGroupResult
	_, err := c.client.From(tableName).
		Select("name", "", false).
		Order("name", &postgrest.OrderOpts{Ascending: true}).
		ExecuteTo(&results)
	if err != nil {
		return nil, fmt.Errorf("failed to get preset groups from %s: %w", tableName, err)
	}

	presetGroups := make([]string, 0, len(results))
	for _, item := range results {
		presetGroups = append(presetGroups, item.Name)
	}

	return presetGroups, nil
}

// GetBatchAppCategories 批量获取多个应用的类别
func (c *DBClient) GetBatchAppCategories(ctx context.Context, appIDs []string) (map[string][]string, error) {
	if len(appIDs) == 0 {
		return map[string][]string{}, nil
	}

	startTime := time.Now()
	defer func() {
		logger.Debug("DB GetBatchAppCategories execution time: %v", time.Since(startTime))
	}()

	// 在 ExecuteTo 之前添加 CategoryResult 结构体定义和 results 变量声明
	type CategoryResult struct {
		AppID      string `json:"app_id"`
		Categories struct {
			Name string `json:"name"`
		} `json:"categories"`
	}

	var results []CategoryResult
	_, err := c.client.From("app_categories").
		Select("app_id,categories(name)", "", false).
		In("app_id", appIDs).
		ExecuteTo(&results)
	if err != nil {
		return nil, fmt.Errorf("failed to get batch app categories: %w", err)
	}

	// 提取类别名称
	categoriesMap := make(map[string][]string)
	for _, item := range results {
		categoriesMap[item.AppID] = append(categoriesMap[item.AppID], item.Categories.Name)
	}

	logger.Debug("Fetched categories for %d apps, found %d category associations",
		len(appIDs), len(results))

	return categoriesMap, nil
}

// GetBatchAppDeeplinks 批量获取多个应用的深度链接
func (c *DBClient) GetBatchAppDeeplinks(ctx context.Context, appIDs []string) (map[string][]Deeplink, error) {
	if len(appIDs) == 0 {
		return map[string][]Deeplink{}, nil
	}

	startTime := time.Now()
	defer func() {
		logger.Debug("DB GetBatchAppDeeplinks execution time: %v", time.Since(startTime))
	}()

	var deeplinks []Deeplink
	_, err := c.client.From("deeplinks").
		Select("*", "", false).
		In("app_id", appIDs).
		Order("name", &postgrest.OrderOpts{Ascending: true}).
		ExecuteTo(&deeplinks)
	if err != nil {
		return nil, fmt.Errorf("failed to get batch app deeplinks: %w", err)
	}

	// 将结果组织成 map[appID][]Deeplink 的形式
	deeplinksMap := make(map[string][]Deeplink)
	for _, dl := range deeplinks {
		appID := dl.AppID.String()
		if appID == "" {
			continue
		}
		deeplinksMap[appID] = append(deeplinksMap[appID], dl)
	}

	logger.Debug("Fetched deeplinks for %d apps, found %d deeplinks total",
		len(appIDs), len(deeplinks))

	return deeplinksMap, nil
}

// GetBatchDeeplinkPresetGroups 批量获取多个深度链接的预设组 (language specific)
func (c *DBClient) GetBatchDeeplinkPresetGroups(ctx context.Context, deeplinkIDs []string, language string) (map[string][]string, error) {
	if len(deeplinkIDs) == 0 {
		return map[string][]string{}, nil
	}

	startTime := time.Now()
	defer func() {
		logger.Debug("DB GetBatchDeeplinkPresetGroups execution time: %v", time.Since(startTime))
	}()

	type PresetGroupResult struct {
		DeeplinkID   string `json:"deeplink_id"`
		PresetGroups struct {
			Name string `json:"name"`
		} `json:"preset_groups"`
	}

	deeplinkPresetGroupsTable := getDeeplinkPresetGroupsTableName(language)
	presetGroupsTable := getPresetGroupsTableName(language) // Table name for the join target
	selectString := fmt.Sprintf("deeplink_id,preset_groups:%s(name)", presetGroupsTable)

	var results []PresetGroupResult
	_, err := c.client.From(deeplinkPresetGroupsTable).
		Select(selectString, "", false).
		In("deeplink_id", deeplinkIDs).
		ExecuteTo(&results)
	if err != nil {
		return nil, fmt.Errorf("failed to get batch deeplink preset groups from %s: %w", deeplinkPresetGroupsTable, err)
	}

	logger.Debug("Preset groups results: %v", results)

	presetGroupsMap := make(map[string][]string)
	for _, item := range results {
		presetGroupsMap[item.DeeplinkID] = append(presetGroupsMap[item.DeeplinkID], item.PresetGroups.Name)
	}

	logger.Debug("Fetched preset groups for %d deeplinks from %s, found %d preset group associations",
		len(deeplinkIDs), deeplinkPresetGroupsTable, len(results))

	return presetGroupsMap, nil
}

// GetPresetApps 获取所有预设的应用 (language specific based on association table)
func (c *DBClient) GetPresetApps(ctx context.Context, language string) ([]App, error) {
	startTime := time.Now()
	defer func() { logger.Debug("DB GetPresetApps execution time: %v", time.Since(startTime)) }()

	deeplinkPresetGroupsTable := getDeeplinkPresetGroupsTableName(language)

	// Fetch deeplink IDs associated with preset groups for the given language
	type DeeplinkPresetGroup struct {
		DeeplinkID string `json:"deeplink_id"`
	}
	var deeplinkPresetGroups []DeeplinkPresetGroup
	_, err := c.client.From(deeplinkPresetGroupsTable).
		Select("deeplink_id", "", false).
		ExecuteTo(&deeplinkPresetGroups)
	if err != nil {
		return nil, fmt.Errorf("failed to get deeplinks with preset groups from %s: %w", deeplinkPresetGroupsTable, err)
	}
	if len(deeplinkPresetGroups) == 0 {
		logger.Debug("No preset deeplinks found in %s for language %s", deeplinkPresetGroupsTable, language)
		return []App{}, nil
	}

	// Deduplicate deeplink IDs
	deeplinkIDMap := make(map[string]bool, len(deeplinkPresetGroups))
	for _, dp := range deeplinkPresetGroups {
		deeplinkIDMap[dp.DeeplinkID] = true
	}
	logger.Debug("Found %d unique preset deeplinks in %s", len(deeplinkIDMap), deeplinkPresetGroupsTable)
	deeplinkIDs := make([]string, 0, len(deeplinkIDMap))
	for id := range deeplinkIDMap {
		deeplinkIDs = append(deeplinkIDs, id)
	}

	// Fetch app IDs associated with these deeplinks
	type DeeplinkAppID struct {
		AppID string `json:"app_id"`
	}
	var deeplinks []DeeplinkAppID
	_, err = c.client.From("deeplinks").
		Select("app_id", "", false).
		In("id", deeplinkIDs).
		ExecuteTo(&deeplinks)
	if err != nil {
		return nil, fmt.Errorf("failed to get apps for preset deeplinks: %w", err)
	}
	if len(deeplinks) == 0 {
		logger.Debug("No apps found associated with the fetched preset deeplinks")
		return []App{}, nil
	}

	// Deduplicate app IDs
	appIDMap := make(map[string]bool, len(deeplinks))
	for _, dl := range deeplinks {
		if dl.AppID != "" {
			appIDMap[dl.AppID] = true
		}
	}
	logger.Debug("Found %d unique apps associated with preset deeplinks", len(appIDMap))
	appIDs := make([]string, 0, len(appIDMap))
	for appID := range appIDMap {
		appIDs = append(appIDs, appID)
	}

	query := c.client.From("apps").
		Select("*", "", false).
		In("id", appIDs).
		Order("app_name", &postgrest.OrderOpts{Ascending: true})

	var apps []App
	_, err = query.ExecuteTo(&apps)
	if err != nil {
		return nil, fmt.Errorf("failed to get preset apps details: %w", err)
	}

	logger.Debug("Found %d preset apps associated via %s", len(apps), deeplinkPresetGroupsTable) // Updated log message
	return apps, nil
}

// LoadMoreApps 加载更多应用 (language specific for preset group filter)
func (c *DBClient) LoadMoreApps(ctx context.Context, offset, limit int32, category, language, presetGroup, searchTerm string) ([]App, bool, error) {
	startTime := time.Now()
	defer func() {
		logger.Debug("DB LoadMoreApps execution time: %v", time.Since(startTime))
	}()

	// Parameter validation
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}
	if category != "" {
		if _, err := uuid.Parse(category); err != nil {
			return nil, false, fmt.Errorf("invalid category ID format: %s", category)
		}
	}
	if presetGroup != "" {
		if _, err := uuid.Parse(presetGroup); err != nil {
			return nil, false, fmt.Errorf("invalid preset group ID format: %s", presetGroup)
		}
	}

	// Query builder function
	buildQuery := func(baseQuery *postgrest.FilterBuilder) *postgrest.FilterBuilder {
		query := baseQuery
		if language != "" {
			query = query.Eq("language", language)
		}
		if searchTerm != "" {
			likePattern := fmt.Sprintf("%%%s%%", searchTerm)
			orFilter := fmt.Sprintf("app_name.ilike.%s,package_name.ilike.%s", likePattern, likePattern)
			query = query.Or(orFilter, "")
		}
		if category != "" {
			query = query.Filter("id", "in.app_categories!app_id", "")
			query = query.Eq("app_categories.category_id", category)
		}
		if presetGroup != "" {
			deeplinkPresetGroupsTable := getDeeplinkPresetGroupsTableName(language)
			deeplinkFilter := fmt.Sprintf("in.%s!deeplink_id", deeplinkPresetGroupsTable)
			presetGroupFilter := fmt.Sprintf("%s.preset_group_id", deeplinkPresetGroupsTable)
			query = query.Filter("id", "in.deeplinks!app_id", "")
			query = query.Filter("deeplinks.id", deeplinkFilter, "")
			query = query.Eq(presetGroupFilter, presetGroup)
		}
		return query
	}

	// Build and execute main query
	mainQuery := buildQuery(c.client.From("apps").Select("id,app_name,package_name,language,icon_url,created_at,updated_at", "", false)).
		Range(int(offset), int(offset+limit-1), "").
		Order("app_name", &postgrest.OrderOpts{Ascending: true})
	var apps []App
	_, err := mainQuery.ExecuteTo(&apps)
	if err != nil {
		return nil, false, fmt.Errorf("failed to load more apps (offset=%d, limit=%d, category=%s, language=%s, presetGroup=%s, searchTerm=%s): %w",
			offset, limit, category, language, presetGroup, searchTerm, err)
	}

	// Check if more data exists
	hasMore := false
	if len(apps) > 0 && len(apps) == int(limit) {
		var nextApp []App
		nextPageQuery := buildQuery(c.client.From("apps").Select("id", "", false)).
			Range(int(offset+limit), int(offset+limit+limit-1), ""). // Check next offset
			Limit(1, "")                                             // Limit to 1
		_, err := nextPageQuery.ExecuteTo(&nextApp)
		if err == nil && len(nextApp) > 0 {
			hasMore = true
		} else if err != nil {
			logger.Warn("Failed to check for more apps: %v", err)
		}
	}

	logger.Debug("LoadMoreApps completed: loaded %d apps (hasMore=%v) with offset=%d, limit=%d", len(apps), hasMore, offset, limit)
	return apps, hasMore, nil
}

// GetPluginTotalCount 获取插件总数
func (c *DBClient) GetPluginTotalCount(ctx context.Context) (int32, error) {
	startTime := time.Now()
	defer func() {
		logger.Debug("DB GetPluginTotalCount execution time: %v", time.Since(startTime))
	}()

	if !c.pluginCountCacheAt.IsZero() && time.Since(c.pluginCountCacheAt) < time.Hour {
		logger.Debug("Using cached plugin count: %d (cached at: %v)", c.pluginCountCache, c.pluginCountCacheAt)
		return c.pluginCountCache, nil
	}

	var ids []struct {
		ID string `json:"id"`
	}
	// Note: This currently counts deeplinks, not distinct apps.
	// Consider changing to count from 'apps' table if total *app* count is needed.
	_, err := c.client.From("deeplinks").Select("id", "", false).ExecuteTo(&ids)
	if err != nil {
		return 0, fmt.Errorf("failed to get plugin total count: %w", err)
	}

	count := int32(len(ids))
	c.pluginCountCache = count
	c.pluginCountCacheAt = time.Now()
	logger.Debug("Updated plugin count cache: %d", count)
	return count, nil
}

// InsertSubmittedDeeplink inserts a new submitted deeplink record.
func (c *DBClient) InsertSubmittedDeeplink(ctx context.Context, deeplink SubmittedDeeplink) (*SubmittedDeeplink, error) {
	var results []SubmittedDeeplink // Use the type directly

	// Create a map containing only the fields we want to insert, excluding timestamps.
	insertData := map[string]interface{}{}

	if deeplink.UserID != nil {
		insertData["user_id"] = *deeplink.UserID
	}
	if deeplink.Deeplink != nil {
		insertData["deeplink"] = *deeplink.Deeplink
	}
	if deeplink.Name != nil {
		insertData["name"] = *deeplink.Name
	}
	if deeplink.Type != nil {
		insertData["type"] = *deeplink.Type
	}
	if deeplink.AppPackageName != nil {
		insertData["app_package_name"] = *deeplink.AppPackageName
	}
	if deeplink.RequiresTextInput != nil {
		insertData["requires_text_input"] = *deeplink.RequiresTextInput
	} // Let DB handle default if nil?
	if deeplink.OpenWithApp != nil {
		insertData["open_with_app"] = *deeplink.OpenWithApp
	}
	if deeplink.OpenWithActivity != nil {
		insertData["open_with_activity"] = *deeplink.OpenWithActivity
	}
	// We explicitly DO NOT add created_at, updated_at, or status to let the DB handle defaults.

	// Execute the insert operation using the map
	// Keep returning parameter as "representation"
	_, err := c.client.From("submitted_deeplinks").
		Insert([]map[string]interface{}{insertData}, false, "", "representation", ""). // Pass slice of map
		ExecuteTo(&results)                                                            // Results should now contain DB-generated timestamps

	if err != nil {
		// Log the error for debugging
		logger.Error("Failed to insert submitted deeplink using map: %v. Data map: %+v", err, insertData)
		return nil, fmt.Errorf("failed to insert submitted deeplink: %w", err)
	}

	if len(results) == 0 {
		logger.Error("Insert operation for submitted deeplink returned no results using map. Data map: %+v", insertData)
		return nil, fmt.Errorf("insert operation returned no results")
	}

	// Return the first result, which should contain the inserted record with its ID and DB-generated values
	return &results[0], nil
}
