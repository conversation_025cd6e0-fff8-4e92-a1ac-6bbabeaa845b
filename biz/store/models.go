package store

import (
	"time"

	"github.com/google/uuid"
)

// App 对应 apps 表
type App struct {
	ID          uuid.UUID `json:"id"`
	AppName     string    `json:"app_name"`
	PackageName string    `json:"package_name"`
	Language    string    `json:"language"`
	IconURL     string    `json:"icon_url,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Category 对应 categories 表
type Category struct {
	ID        uuid.UUID `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
}

// Deeplink 对应 deeplinks 表
type Deeplink struct {
	ID                uuid.UUID `json:"id"`
	AppID             uuid.UUID `json:"app_id"`
	Name              string    `json:"name"`
	DeeplinkURL       string    `json:"deeplink"`
	Type              string    `json:"type"`
	Verified          bool      `json:"verified"`
	RequiresTextInput bool      `json:"requires_text_input"`
	OpenWithApp       string    `json:"open_with_app,omitempty"`
	OpenWithActivity  string    `json:"open_with_activity,omitempty"`
	Config            string    `json:"config,omitempty"`
	UsageCount        int       `json:"usage_count"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// PresetGroup 对应 preset_groups 表
type PresetGroup struct {
	ID        uuid.UUID `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
}

// PluginDetail 完整的插件详情，包含应用信息和深度链接
type PluginDetail struct {
	App        App                        `json:"app"`
	Categories []string                   `json:"categories"`
	Deeplinks  []DeeplinkWithPresetGroups `json:"deeplinks"`
}

// DeeplinkWithPresetGroups 包含预设组的深度链接信息
type DeeplinkWithPresetGroups struct {
	Deeplink     Deeplink `json:"deeplink"`
	PresetGroups []string `json:"preset_groups"`
}

// PaginationResult 分页结果
type PaginationResult struct {
	Data       []PluginDetail `json:"data"`
	Total      int            `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// SubmittedDeeplink 对应 submitted_deeplinks 表
type SubmittedDeeplink struct {
	ID                int64      `json:"id,omitempty" pk:"true"`        // Use omitempty for auto-generated fields
	UserID            *uuid.UUID `json:"user_id,omitempty"`             // Pointer for nullable UUID
	Deeplink          *string    `json:"deeplink,omitempty"`            // Pointer for nullable text
	Name              *string    `json:"name,omitempty"`                // Pointer for nullable text
	Type              *string    `json:"type,omitempty"`                // Pointer for nullable text
	RequiresTextInput *bool      `json:"requires_text_input,omitempty"` // Pointer for nullable boolean
	OpenWithApp       *string    `json:"open_with_app,omitempty"`       // Pointer for nullable text
	OpenWithActivity  *string    `json:"open_with_activity,omitempty"`  // Pointer for nullable text
	Status            *string    `json:"status,omitempty"`              // Assuming deeplink_status is text-like in Go
	AppPackageName    *string    `json:"app_package_name,omitempty"`    // Pointer for nullable text
	CreatedAt         time.Time  `json:"created_at,omitempty"`
	UpdatedAt         time.Time  `json:"updated_at,omitempty"`
}
