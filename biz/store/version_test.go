package store

import (
	"testing"
)

func TestParseVersion(t *testing.T) {
	tests := []struct {
		input    string
		expected *Version
		hasError bool
	}{
		{"2.3.0", &Version{2, 3, 0}, false},
		{"v2.3.0", &Version{2, 3, 0}, false},
		{"1.0.0", &Version{1, 0, 0}, false},
		{"3.1.5", &Version{3, 1, 5}, false},
		{"invalid", nil, true},
		{"2.3", nil, true},
		{"2.3.0.1", nil, true},
	}

	for _, test := range tests {
		result, err := ParseVersion(test.input)

		if test.hasError {
			if err == nil {
				t.Errorf("Expected error for input %s, but got none", test.input)
			}
		} else {
			if err != nil {
				t.<PERSON>rrorf("Unexpected error for input %s: %v", test.input, err)
			}
			if result == nil || *result != *test.expected {
				t.<PERSON>("For input %s, expected %v, got %v", test.input, test.expected, result)
			}
		}
	}
}

func TestVersionIsLessOrEqual(t *testing.T) {
	tests := []struct {
		version1 *Version
		version2 *Version
		expected bool
	}{
		{&Version{2, 3, 0}, &Version{2, 3, 0}, true},  // 相等
		{&Version{2, 2, 9}, &Version{2, 3, 0}, true},  // 小于
		{&Version{2, 3, 1}, &Version{2, 3, 0}, false}, // 大于
		{&Version{1, 9, 9}, &Version{2, 0, 0}, true},  // 主版本小于
		{&Version{3, 0, 0}, &Version{2, 9, 9}, false}, // 主版本大于
		{&Version{2, 2, 0}, &Version{2, 3, 0}, true},  // 次版本小于
		{&Version{2, 4, 0}, &Version{2, 3, 0}, false}, // 次版本大于
	}

	for _, test := range tests {
		result := test.version1.IsLessOrEqual(test.version2)
		if result != test.expected {
			t.Errorf("For %s <= %s, expected %v, got %v",
				test.version1.String(), test.version2.String(), test.expected, result)
		}
	}
}

func TestShouldFilterWebhook(t *testing.T) {
	tests := []struct {
		appVersion string
		expected   bool
		desc       string
	}{
		{"", true, "空版本应该过滤"},
		{"invalid", true, "无效版本应该过滤"},
		{"2.3.0", true, "2.3.0应该过滤"},
		{"2.2.9", true, "2.2.9应该过滤"},
		{"1.0.0", true, "1.0.0应该过滤"},
		{"2.3.1", false, "2.3.1不应该过滤"},
		{"2.4.0", false, "2.4.0不应该过滤"},
		{"3.0.0", false, "3.0.0不应该过滤"},
		{"v2.3.0", true, "v2.3.0应该过滤"},
		{"v2.3.1", false, "v2.3.1不应该过滤"},
	}

	for _, test := range tests {
		result := ShouldFilterWebhook(test.appVersion)
		if result != test.expected {
			t.Errorf("%s: 对于版本 %s, 期望 %v, 得到 %v",
				test.desc, test.appVersion, test.expected, result)
		}
	}
}
