package store

import (
	"context"
	"fmt"
	"net/http"

	"connectrpc.com/connect"
	v1 "fastserver.com/fastserver/gen/proto/store/v1"
	"fastserver.com/fastserver/utils/logger"
)

// StoreHandler 实现 StoreService 接口
type StoreHandler struct {
	service *StoreService
}

// NewStoreHandler 创建一个新的 StoreHandler
func NewStoreHandler() (*StoreHandler, error) {
	service, err := NewStoreService()
	if err != nil {
		return nil, fmt.Errorf("failed to create store service: %w", err)
	}

	return &StoreHandler{
		service: service,
	}, nil
}

// GetPlugin 获取单个插件详情
func (h *StoreHandler) GetPlugin(
	ctx context.Context,
	req *connect.Request[v1.GetPluginRequest],
) (*connect.Response[v1.GetPluginResponse], error) {
	logger.Info("Received GetPlugin request: id=%s", req.Msg.Id)

	plugin, err := h.service.GetPlugin(ctx, req.Msg.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get plugin: %w", err)
	}

	appVersion := getAppVersionFromHeader(req.Header())
	if ShouldFilterWebhook(appVersion) {
		filteredPlugin := filterWebhookDeeplinks(*plugin)
		plugin = &filteredPlugin
	}

	return connect.NewResponse(&v1.GetPluginResponse{
		Plugin: convertToProtoPluginDetail(*plugin),
	}), nil
}

// GetCategories 获取所有类别
func (h *StoreHandler) GetCategories(
	ctx context.Context,
	req *connect.Request[v1.GetCategoriesRequest],
) (*connect.Response[v1.GetCategoriesResponse], error) {
	logger.Info("Received GetCategories request")

	categories, err := h.service.GetCategories(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	return connect.NewResponse(&v1.GetCategoriesResponse{
		Categories: categories,
	}), nil
}

// GetPresetGroups 获取所有预设组
func (h *StoreHandler) GetPresetGroups(
	ctx context.Context,
	req *connect.Request[v1.GetPresetGroupsRequest],
) (*connect.Response[v1.GetPresetGroupsResponse], error) {
	language := getLanguageFromHeader(req.Header())

	logger.Info("Received GetPresetGroups request, language from header=%s", language)

	presetGroups, err := h.service.GetPresetGroups(ctx, language)
	if err != nil {
		return nil, fmt.Errorf("failed to get preset groups for language %s: %w", language, err)
	}

	return connect.NewResponse(&v1.GetPresetGroupsResponse{
		PresetGroups: presetGroups,
	}), nil
}

// GetPresetPlugins 获取所有预设的插件
func (h *StoreHandler) GetPresetPlugins(
	ctx context.Context,
	req *connect.Request[v1.GetPresetPluginsRequest],
) (*connect.Response[v1.GetPresetPluginsResponse], error) {
	language := getLanguageFromHeader(req.Header())
	appVersion := getAppVersionFromHeader(req.Header())

	logger.Info("Received GetPresetPlugins request, language from header=%s, app version=%s", language, appVersion)

	plugins, err := h.service.GetPresetPlugins(ctx, language)
	if err != nil {
		return nil, fmt.Errorf("failed to get preset plugins for language %s: %w", language, err)
	}

	// 检查是否需要过滤webhook插件
	if ShouldFilterWebhook(appVersion) {
		plugins = filterWebhookPlugins(plugins)
		logger.Info("Filtered webhook plugins for app version %s", appVersion)
	}

	protoPlugins := make([]*v1.PluginDetail, 0, len(plugins))
	for _, p := range plugins {
		protoPlugins = append(protoPlugins, convertToProtoPluginDetail(p))
	}

	return connect.NewResponse(&v1.GetPresetPluginsResponse{
		Plugins: protoPlugins,
	}), nil
}

// LoadMorePlugins 加载更多插件
func (h *StoreHandler) LoadMorePlugins(
	ctx context.Context,
	req *connect.Request[v1.LoadMorePluginsRequest],
) (*connect.Response[v1.LoadMorePluginsResponse], error) {
	// 添加参数验证
	if req.Msg.Limit <= 0 {
		req.Msg.Limit = 20 // 默认值
	}
	if req.Msg.Limit > 100 {
		req.Msg.Limit = 100 // 最大限制
	}
	if req.Msg.Offset < 0 {
		req.Msg.Offset = 0
	}

	appVersion := getAppVersionFromHeader(req.Header())

	logger.Info("Received LoadMorePlugins request: offset=%d, limit=%d, category=%s, language=%s, presetGroup=%s, searchTerm=%s, app version=%s",
		req.Msg.Offset, req.Msg.Limit, req.Msg.Category, req.Msg.Language, req.Msg.PresetGroup, req.Msg.SearchTerm, appVersion)

	plugins, hasMore, nextOffset, totalCount, err := h.service.LoadMorePlugins(
		ctx,
		req.Msg.Offset,
		req.Msg.Limit,
		req.Msg.Category,
		req.Msg.Language,
		req.Msg.PresetGroup,
		req.Msg.SearchTerm,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load more plugins: %w", err)
	}

	// 检查是否需要过滤webhook插件
	if ShouldFilterWebhook(appVersion) {
		plugins = filterWebhookPlugins(plugins)
		logger.Info("Filtered webhook plugins for app version %s", appVersion)
	}

	// 转换为 proto 消息
	protoPlugins := make([]*v1.PluginDetail, 0, len(plugins))
	for _, p := range plugins {
		protoPlugins = append(protoPlugins, convertToProtoPluginDetail(p))
	}

	logger.Info("LoadMorePlugins completed: returned %d plugins, hasMore=%v, nextOffset=%d, totalCount=%d",
		len(protoPlugins), hasMore, nextOffset, totalCount)

	return connect.NewResponse(&v1.LoadMorePluginsResponse{
		Plugins:    protoPlugins,
		HasMore:    hasMore,
		NextOffset: nextOffset,
		TotalCount: totalCount,
	}), nil
}

// filterWebhookPlugins 过滤掉包含webhook类型deeplinks的插件
func filterWebhookPlugins(plugins []PluginDetail) []PluginDetail {
	filtered := make([]PluginDetail, 0, len(plugins))

	for _, plugin := range plugins {
		filteredPlugin := filterWebhookDeeplinks(plugin)
		// 只有当插件还有非webhook的deeplinks时才保留
		if len(filteredPlugin.Deeplinks) > 0 {
			filtered = append(filtered, filteredPlugin)
		}
	}

	return filtered
}

// filterWebhookDeeplinks 过滤掉webhook类型的deeplinks
func filterWebhookDeeplinks(plugin PluginDetail) PluginDetail {
	filteredDeeplinks := make([]DeeplinkWithPresetGroups, 0, len(plugin.Deeplinks))

	for _, dl := range plugin.Deeplinks {
		// webhook 和 action 类型的插件都是 2.3.0 版本之后才支持的
		if dl.Deeplink.Type != "webhook" && dl.Deeplink.Type != "action" {
			filteredDeeplinks = append(filteredDeeplinks, dl)
		}
	}

	return PluginDetail{
		App:        plugin.App,
		Categories: plugin.Categories,
		Deeplinks:  filteredDeeplinks,
	}
}

// convertToProtoPluginDetail 将内部 PluginDetail 转换为 proto PluginDetail
func convertToProtoPluginDetail(p PluginDetail) *v1.PluginDetail {
	plugin := &v1.Plugin{
		Id:          p.App.ID.String(),
		AppName:     p.App.AppName,
		PackageName: p.App.PackageName,
		Language:    p.App.Language,
		IconUrl:     p.App.IconURL,
		Categories:  p.Categories,
	}

	deeplinks := make([]*v1.Deeplink, 0, len(p.Deeplinks))
	for _, dl := range p.Deeplinks {
		deeplinks = append(deeplinks, &v1.Deeplink{
			Id:                dl.Deeplink.ID.String(),
			Name:              dl.Deeplink.Name,
			Deeplink:          dl.Deeplink.DeeplinkURL,
			Type:              dl.Deeplink.Type,
			Verified:          dl.Deeplink.Verified,
			RequiresTextInput: dl.Deeplink.RequiresTextInput,
			OpenWithApp:       dl.Deeplink.OpenWithApp,
			OpenWithActivity:  dl.Deeplink.OpenWithActivity,
			PresetGroups:      dl.PresetGroups,
			Config:            dl.Deeplink.Config,
		})
	}

	return &v1.PluginDetail{
		Plugin:    plugin,
		Deeplinks: deeplinks,
	}
}

// getAppVersionFromHeader 从请求头提取app版本信息
func getAppVersionFromHeader(header http.Header) string {
	appVersion := header.Get("bluespace-app-version")
	return appVersion
}

// getLanguageFromHeader 从请求头提取语言代码 ("en", "zh")，默认为 "zh".
func getLanguageFromHeader(header http.Header) string {
	language := header.Get("Language")
	if language == "" {
		language = header.Get("language") // Try lowercase
	}
	if language == "" {
		language = "zh" // Default
	}
	return language
}
