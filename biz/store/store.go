package store

import (
	"context"
	"fmt"
	"time"

	"fastserver.com/fastserver/utils/logger"
	"github.com/google/uuid"
	"golang.org/x/sync/errgroup"
)

// StoreService 插件商店服务
type StoreService struct {
	db *DBClient
}

// NewStoreService 创建一个新的插件商店服务
func NewStoreService() (*StoreService, error) {
	db, err := NewDBClient()
	if err != nil {
		return nil, fmt.Errorf("failed to create database client: %w", err)
	}

	return &StoreService{
		db: db,
	}, nil
}

// Close 关闭服务
func (s *StoreService) Close() {
	if s.db != nil {
		s.db.Close()
	}
}

// GetPlugin 获取单个插件的详细信息
func (s *StoreService) GetPlugin(ctx context.Context, id string) (*PluginDetail, error) {
	pluginID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid plugin ID: %w", err)
	}

	detail, err := s.getPluginDetail(ctx, pluginID)
	if err != nil {
		return nil, err
	}

	return &detail, nil
}

// GetCategories 获取所有可用的类别
func (s *StoreService) GetCategories(ctx context.Context) ([]string, error) {
	return s.db.GetCategories(ctx)
}

// GetPresetGroups 获取所有可用的预设组
// language: "zh" for Chinese (default), other values for English tables
func (s *StoreService) GetPresetGroups(ctx context.Context, language string) ([]string, error) {
	return s.db.GetPresetGroups(ctx, language)
}

// GetPresetPlugins 获取所有预设的插件
func (s *StoreService) GetPresetPlugins(ctx context.Context, language string) ([]PluginDetail, error) {
	startTime := time.Now()
	defer func() {
		logger.Debug("GetPresetPlugins total execution time: %v", time.Since(startTime))
	}()

	listAppsStart := time.Now()
	apps, err := s.db.GetPresetApps(ctx, language)
	logger.Debug("GetPresetApps execution time: %v", time.Since(listAppsStart))
	if err != nil {
		return nil, fmt.Errorf("failed to get preset apps: %w", err)
	}

	if len(apps) == 0 {
		logger.Debug("No preset apps found")
		return []PluginDetail{}, nil
	}

	batchStart := time.Now()
	appIDs := make([]string, 0, len(apps))
	for _, app := range apps {
		appIDs = append(appIDs, app.ID.String())
	}

	details, err := s.getBatchPluginDetails(ctx, apps, appIDs, language)
	logger.Debug("Batch details fetch time: %v", time.Since(batchStart))
	if err != nil {
		return nil, fmt.Errorf("failed to get batch details: %w", err)
	}

	logger.Debug("GetPresetPlugins completed: %d plugins", len(details))

	return details, nil
}

// getPluginDetail 获取插件的详细信息，包括类别和深度链接
// Note: This function is primarily used by GetPlugin which doesn't have a language context.
// It calls the non-language-specific DB function for deeplink groups.
func (s *StoreService) getPluginDetail(ctx context.Context, appID uuid.UUID) (PluginDetail, error) {
	// 获取应用信息
	app, err := s.db.GetAppByID(ctx, appID.String())
	if err != nil {
		return PluginDetail{}, fmt.Errorf("failed to get app: %w", err)
	}

	// 获取应用的类别
	categories, err := s.db.GetAppCategories(ctx, appID.String())
	if err != nil {
		return PluginDetail{}, fmt.Errorf("failed to get app categories: %w", err)
	}

	// 获取应用的深度链接
	deeplinks, err := s.db.GetAppDeeplinks(ctx, appID.String())
	if err != nil {
		return PluginDetail{}, fmt.Errorf("failed to get app deeplinks: %w", err)
	}

	// 获取每个深度链接的预设组 (使用默认语言表)
	deeplinkWithGroups := make([]DeeplinkWithPresetGroups, 0, len(deeplinks))
	for _, dl := range deeplinks {
		deeplinkWithGroups = append(deeplinkWithGroups, DeeplinkWithPresetGroups{
			Deeplink:     dl,
			PresetGroups: []string{},
		})
	}

	return PluginDetail{
		App:        *app,
		Categories: categories,
		Deeplinks:  deeplinkWithGroups,
	}, nil
}

func (s *StoreService) getBatchPluginDetails(ctx context.Context, apps []App, appIDs []string, language string) ([]PluginDetail, error) {
	if len(apps) == 0 {
		return []PluginDetail{}, nil
	}

	var g errgroup.Group
	var appCategories map[string][]string
	var appDeeplinks map[string][]Deeplink

	// 1. 并发获取所有应用的类别
	g.Go(func() error {
		var err error
		categoriesStart := time.Now()
		appCategories, err = s.db.GetBatchAppCategories(ctx, appIDs)
		logger.Debug("Batch categories fetch time: %v", time.Since(categoriesStart))
		if err != nil {
			return fmt.Errorf("failed to get batch app categories: %w", err)
		}
		return nil
	})

	// 2. 并发获取所有应用的深度链接
	g.Go(func() error {
		var err error
		deeplinksStart := time.Now()
		appDeeplinks, err = s.db.GetBatchAppDeeplinks(ctx, appIDs)
		logger.Debug("Batch deeplinks fetch time: %v", time.Since(deeplinksStart))
		if err != nil {
			return fmt.Errorf("failed to get batch app deeplinks: %w", err)
		}
		return nil
	})

	// 等待并发获取类别和 Deeplink 完成
	if err := g.Wait(); err != nil {
		return nil, err // Return the first error encountered
	}

	// 3. 提取所有深度链接ID (在前两步完成后执行)
	allDeeplinkIDs := make([]string, 0)
	for _, deeplinks := range appDeeplinks {
		for _, dl := range deeplinks {
			allDeeplinkIDs = append(allDeeplinkIDs, dl.ID.String())
		}
	}

	// 4. 获取所有深度链接的预设组 (在前两步完成后执行)
	presetGroupsStart := time.Now()
	deeplinkPresetGroups := make(map[string][]string)
	if len(allDeeplinkIDs) > 0 {
		var err error
		deeplinkPresetGroups, err = s.db.GetBatchDeeplinkPresetGroups(ctx, allDeeplinkIDs, language)
		if err != nil {
			logger.Error("Failed to get batch deeplink preset groups (proceeding anyway): %v", err)
		}
	}

	logger.Debug("Preset groups: %v", deeplinkPresetGroups)
	logger.Debug("Batch preset groups fetch time: %v", time.Since(presetGroupsStart))

	// 5. 组装结果 (与之前相同)
	assembleStart := time.Now()
	result := make([]PluginDetail, 0, len(apps))

	for _, app := range apps {
		appID := app.ID.String()

		// Check if appCategories was successfully fetched
		var categories []string
		if appCategories != nil {
			categories, _ = appCategories[appID] // Use default empty slice if not found
		} else {
			categories = []string{}
		}

		// Check if appDeeplinks was successfully fetched
		var deeplinks []Deeplink
		if appDeeplinks != nil {
			deeplinks, _ = appDeeplinks[appID]
		} else {
			deeplinks = []Deeplink{}
		}

		deeplinkWithGroups := make([]DeeplinkWithPresetGroups, 0, len(deeplinks))
		for _, dl := range deeplinks {
			dlID := dl.ID.String()
			presetGroups, ok := deeplinkPresetGroups[dlID] // Use fetched preset groups
			if !ok {
				presetGroups = []string{}
			}

			deeplinkWithGroups = append(deeplinkWithGroups, DeeplinkWithPresetGroups{
				Deeplink:     dl,
				PresetGroups: presetGroups,
			})
		}

		detail := PluginDetail{
			App:        app,
			Categories: categories,
			Deeplinks:  deeplinkWithGroups,
		}

		result = append(result, detail)
	}
	logger.Debug("Data assembly time: %v", time.Since(assembleStart))

	return result, nil
}

// LoadMorePlugins 加载更多插件
func (s *StoreService) LoadMorePlugins(ctx context.Context, offset, limit int32, category, language, presetGroup, searchTerm string) ([]PluginDetail, bool, int32, int32, error) {
	startTime := time.Now()
	defer func() {
		logger.Debug("LoadMorePlugins total execution time: %v", time.Since(startTime))
	}()

	listAppsStart := time.Now()
	apps, hasMore, err := s.db.LoadMoreApps(ctx, offset, limit, category, language, presetGroup, searchTerm)
	logger.Debug("LoadMoreApps execution time: %v", time.Since(listAppsStart))
	if err != nil {
		return nil, false, 0, 0, fmt.Errorf("failed to load more apps: %w", err)
	}

	if len(apps) == 0 {
		return []PluginDetail{}, false, offset, 0, nil
	}

	batchStart := time.Now()
	appIDs := make([]string, 0, len(apps))
	for _, app := range apps {
		appIDs = append(appIDs, app.ID.String())
	}

	details, err := s.getBatchPluginDetails(ctx, apps, appIDs, language)
	logger.Debug("Batch details fetch time: %v", time.Since(batchStart))
	if err != nil {
		return nil, false, 0, 0, fmt.Errorf("failed to get batch details: %w", err)
	}

	nextOffset := offset + int32(len(details))

	totalCount, _ := s.db.GetPluginTotalCount(ctx)

	return details, hasMore, nextOffset, totalCount, nil
}
