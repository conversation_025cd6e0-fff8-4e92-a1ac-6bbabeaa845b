package config

import (
	_ "embed"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

//go:embed config.yaml
var configFileData []byte

// Config 全局配置结构
type Config struct {
	Server *ServerConfig `yaml:"server" json:"server"`
	Auth   *AuthConfig   `yaml:"auth" json:"auth"`
	Log    *LogConfig    `yaml:"log" json:"log"`
	AI     *AIConfig     `yaml:"ai" json:"ai"`
	STT    *STTConfig    `yaml:"stt" json:"stt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int    `yaml:"port"`
	Env  string `yaml:"env"`
}

// IsDevelopment 判断当前是否为开发环境
func (s *ServerConfig) IsDevelopment() bool {
	return s.Env == "development"
}

// IsProduction 判断当前是否为生产环境
func (s *ServerConfig) IsProduction() bool {
	return s.Env == "production"
}

// IsStaging 判断当前是否为测试环境
func (s *ServerConfig) IsStaging() bool {
	return s.Env == "staging"
}

// AuthConfig 认证配置
type AuthConfig struct {
	Supabase     *SupabaseConfig `yaml:"supabase"`
	Cache        *CacheConfig    `yaml:"cache"`
	ExcludePaths []string        `yaml:"exclude_paths" json:"exclude_paths"`
}

// SupabaseConfig Supabase配置
type SupabaseConfig struct {
	URL string `yaml:"url"`
	Key string `yaml:"anon_key"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	duration    time.Duration // 实际使用的 Duration
	DurationStr string        `yaml:"duration"` // 从配置文件读取的字符串
}

// UnmarshalYAML 实现自定义的 YAML 解析
func (c *CacheConfig) UnmarshalYAML(value *yaml.Node) error {
	type rawConfig struct {
		Duration string `yaml:"duration"`
	}
	raw := &rawConfig{}
	if err := value.Decode(raw); err != nil {
		return err
	}

	duration, err := time.ParseDuration(raw.Duration)
	if err != nil {
		return fmt.Errorf("invalid duration format: %w", err)
	}

	c.duration = duration
	c.DurationStr = raw.Duration
	return nil
}

// Duration 获取解析后的时间间隔
func (c *CacheConfig) Duration() time.Duration {
	return c.duration
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
}

// AIConfig AI配置
type AIConfig struct {
	Provider string   `yaml:"provider"`
	Model    string   `yaml:"model"`
	Models   []string `yaml:"models"`
	Endpoint string   `yaml:"endpoint"`
	Token    string   `yaml:"token"`
}

// STTConfig STT配置
type STTConfig struct {
	Provider       string                   `yaml:"provider" json:"provider"` // "gemini" 或 "google-cloud-stt"
	Gemini         *STTGeminiConfig         `yaml:"gemini" json:"gemini"`
	GoogleCloudSTT *STTGoogleCloudSTTConfig `yaml:"google_cloud_stt" json:"google_cloud_stt"`
}

// STTGeminiConfig Gemini STT配置
type STTGeminiConfig struct {
	Model string `yaml:"model" json:"model"`
	Token string `yaml:"token" json:"token"`
}

// STTGoogleCloudSTTConfig Google Cloud Speech-to-Text配置
type STTGoogleCloudSTTConfig struct {
	APIKey    string                    `yaml:"api_key" json:"api_key"`
	ProjectID string                    `yaml:"project_id" json:"project_id"`
	Options   *STTGoogleCloudSTTOptions `yaml:"options" json:"options"`
}

// STTGoogleCloudSTTOptions Google Cloud Speech-to-Text选项
type STTGoogleCloudSTTOptions struct {
	UseEnhanced          bool   `yaml:"use_enhanced" json:"use_enhanced"`
	AutomaticPunctuation bool   `yaml:"automatic_punctuation" json:"automatic_punctuation"`
	Model                string `yaml:"model" json:"model"`
	ProfanityFilter      bool   `yaml:"profanity_filter" json:"profanity_filter"`
	EnableWordConfidence bool   `yaml:"enable_word_confidence" json:"enable_word_confidence"`
}

var (
	// GlobalConfig 全局配置实例
	GlobalConfig *Config
)

// Load 加载配置
func Load() error {
	// 首先尝试从嵌入的配置文件加载
	config := &Config{}
	if err := yaml.Unmarshal(configFileData, config); err != nil {
		return fmt.Errorf("parse embedded config file failed: %w", err)
	}

	// 如果指定了外部配置文件，则尝试从外部加载覆盖默认配置
	configPath := os.Getenv("CONFIG_PATH")
	if configPath != "" {
		data, err := os.ReadFile(filepath.Clean(configPath))
		if err == nil {
			// 只有在成功读取外部配置文件时才尝试解析
			if err := yaml.Unmarshal(data, config); err != nil {
				return fmt.Errorf("parse external config file failed: %w", err)
			}
		}
	}

	// 从环境变量覆盖配置
	if envPort := os.Getenv("SERVER_PORT"); envPort != "" {
		var port int
		if _, err := fmt.Sscanf(envPort, "%d", &port); err == nil {
			config.Server.Port = port
		}
	}

	// 从环境变量覆盖 server.env
	if envEnv := os.Getenv("FAST_SERVER_ENV"); envEnv != "" {
		config.Server.Env = envEnv
	}

	if envKey := os.Getenv("SUPABASE_SERVICE_ROLE_KEY"); envKey != "" {
		config.Auth.Supabase.Key = envKey
	}

	if envURL := os.Getenv("SUPABASE_URL"); envURL != "" {
		config.Auth.Supabase.URL = envURL
	}

	// 验证必要的配置
	if err := validate(config); err != nil {
		return fmt.Errorf("validate config failed: %w", err)
	}

	GlobalConfig = config
	return nil
}

// validate 验证配置
func validate(config *Config) error {
	if config.Server == nil {
		return fmt.Errorf("server config is required")
	}
	if config.Server.Port <= 0 {
		return fmt.Errorf("invalid server port")
	}
	if config.Auth == nil || config.Auth.Supabase == nil {
		return fmt.Errorf("auth config is required")
	}
	if config.Auth.Supabase.URL == "" {
		return fmt.Errorf("supabase URL is required")
	}
	if config.Auth.Supabase.Key == "" {
		return fmt.Errorf("supabase anon key is required")
	}
	if config.Auth.Cache == nil {
		return fmt.Errorf("cache config is required")
	}
	if config.Auth.Cache.Duration() <= 0 {
		return fmt.Errorf("invalid cache duration")
	}

	// 验证STT配置（可选但如果存在必须有效）
	if config.STT != nil {
		if config.STT.Provider == "" {
			return fmt.Errorf("stt provider is required when stt config is present")
		}
		if config.STT.Provider == "google-cloud-stt" {
			if config.STT.GoogleCloudSTT == nil {
				return fmt.Errorf("google cloud stt config is required when provider is google-cloud-stt")
			}
			if config.STT.GoogleCloudSTT.APIKey == "" {
				return fmt.Errorf("google cloud stt api key is required")
			}
			if config.STT.GoogleCloudSTT.ProjectID == "" {
				return fmt.Errorf("google cloud stt project id is required")
			}
		}
	}

	return nil
}

// GetConfig 获取配置实例
func GetConfig() *Config {
	return GlobalConfig
}
