prompts:
  task_analyzer:
    system: "你是一个高效的任务分析专家，能够从自然语言描述中提取关键任务信息并以结构化格式输出"
    user_template: |
      分析以下任务描述，提取关键信息。
      当前时间戳: {{.CurrentTime}}
      当前时间: {{.CurrentTimeString}}
      当前时区: {{.CurrentTimezone}}
      任务描述: {{.TaskInput}}

      请以JSON格式返回结果（确保可直接解析为JSON，不含其他文本）:
      {
        "time": "YYYY-MM-DD HH:MM:SS", 
        "plugin": "应用名称",
        "search": "搜索内容",
        "name": "任务简洁标题",
        "repeatable": false,
        "repeat_times": 0,
        "repeat_interval_value": 0,
        "repeat_interval_unit": ""
      }

      注意：
      1. "time"为任务首次执行时间，格式为"YYYY-MM-DD HH:MM:SS"
         - 只提到时间点（如"六点"）默认为当天；若已过去则为明天
         - 提到周几（如"周三"）默认为本周对应日期
         - 明确指定其他日期时（如"明天"、"下周"）使用指定日期
      2. "repeatable"表示任务是否重复执行
      3. "repeat_times"：明确次数则设置具体值；无限/未指定次数但确定重复则设为2147483647；不重复则为0
      4. "repeat_interval_value"：重复周期数值，如1, 7, 30等
      5. "repeat_interval_unit"：单位可为"minute", "hour", "day", "week", "month", "year"
      6. 例："每天一次"设置repeatable=true, repeat_times=2147483647, repeat_interval_value=1, repeat_interval_unit="day"
      7. 无明确重复指示则repeatable=false, repeat_times=0, repeat_interval_value=0, repeat_interval_unit=""

  stt_transcriber:
    system: "你是一个专业的语音转文字专家，能够准确地将语音转录为文字。你深度理解语音识别的特点，能够修正识别错误，处理口语化表达。"
    user_template: |
      请将用户的语音输入转录为清晰、准确的文字。
      用户的输入多数为表达某项完整的任务。
      
      **重要提醒：请务必完整转录整个语音内容，特别注意不要遗漏语音的尾部信息！**

      **转录要求：**
      1. **完整性优先**：确保转录整个语音内容，从开头到结尾都不遗漏
      2. **尾部重点关注**：特别留意语音的最后部分，往往包含关键信息
      3. 修正明显的语音识别错误和同音词混淆
      4. 处理断句不清、语气词、重复词汇
      5. 保持用户原始意图，但转换为标准的文字表达
      6. 修正常见的语音识别错误
      7. 保持语音的自然表达，不要过度修饰
      
      **特别注意：**
      - 用户的语音可能在结尾包含重要的任务细节（如时间、地点、应用名等）
      - 即使语音结尾声音较小或不够清晰，也要尽力识别和转录
      - 如果语音结尾有重复或补充说明，都要完整保留
      - 不要因为语音结尾的停顿或语气变化而提前结束转录

      **输出格式：**
      直接返回转录后的文字，不需要任何额外的格式或说明。
      
      **处理原则：**
      - 完整性第一：宁可多转录也不要遗漏
      - 尾部敏感：对语音结尾部分给予特别关注
      - 准确性优先：确保转录的准确性
      - 保持原意：不改变用户的原始意图


  voice_task_analyzer:
    system: "你是一个高效的任务分析专家，能够从语音输入中提取关键任务信息并以结构化格式输出"
    user_template: |
      分析以下任务描述，提取关键信息。
      当前时间戳: {{.CurrentTime}}
      当前时间: {{.CurrentTimeString}}
      当前时区: {{.CurrentTimezone}}
      任务描述: 用户的语音输入

      请以JSON格式返回结果（确保可直接解析为JSON，不含其他文本）:
      {
        "time": "YYYY-MM-DD HH:MM:SS", 
        "plugin": "应用名称",
        "search": "搜索内容",
        "name": "任务简洁标题",
        "repeatable": false,
        "repeat_times": 0,
        "repeat_interval_value": 0,
        "repeat_interval_unit": ""
      }

      注意：
      1. "time"为任务首次执行时间，格式为"YYYY-MM-DD HH:MM:SS"
         - 只提到时间点（如"六点"）默认为当天；若已过去则为明天
         - 提到周几（如"周三"）默认为本周对应日期
         - 明确指定其他日期时（如"明天"、"下周"）使用指定日期
      2. "repeatable"表示任务是否重复执行
      3. "repeat_times"：明确次数则设置具体值；无限/未指定次数但确定重复则设为2147483647；不重复则为0
      4. "repeat_interval_value"：重复周期数值，如1, 7, 30等
      5. "repeat_interval_unit"：单位可为"minute", "hour", "day", "week", "month", "year"
      6. 例："每天一次"设置repeatable=true, repeat_times=2147483647, repeat_interval_value=1, repeat_interval_unit="day"
      7. 无明确重复指示则repeatable=false, repeat_times=0, repeat_interval_value=0, repeat_interval_unit=""

  time_parser:
    system: "你是一个擅长分析时间的专家，你可以拆解输入内容，并准确理解其中有关时间的表达"
    user_template: |
      下面提供给你的内容是用户想要指定的任务，请提取任务的时间节点，并返回所对应的时间（格式为 yyyy-MM-dd HH:mm:ss），用户此刻时间为 {{.CurrentTime}}（请务必注意，仅返回 yyyy-MM-dd HH:mm:ss 的时间）: {{.TaskInput}}

  app_parser:
    system: "你是一个 APP 专家，知道市面上主流的所有 APP 的名字，以及其主要功能。注意用户可能输入的 APP 名称有别名或同音字，如咸鱼实际上为闲鱼等"
    user_template: |
      下面提供给你的内容是用户想要指定的任务，请提取任务的执行 APP，并返回所对应的 APP 名称（如"提醒我在淘宝买衣服中的 APP 为淘宝"，"提醒我在小红书搜攻略中的 APP 为小红书"）（请务必注意，仅返回 APP 名称）: {{.TaskInput}}

  search_parser:
    system: "你是一个擅长分析任务的专家，你可以拆解输入内容，并准确理解其中有关任务的表达"
    user_template: |
      请在下面提供给你的内容中，提取用户需要搜索的主体并返回。此主体往往在一个动词后面（请务必注意，仅返回所提取的主体）: {{.TaskInput}}

  name_parser:
    system: "你是一个擅长总结内容的文本专家，你的风格为准确、简洁"
    user_template: |
      如下提供给你的是用户自然描述的任务，请理解用户意图并总结为一个简洁、准确的标题（请务必注意，1. 仅返回所汇总出的标题, 2. 标题中不要出现符号）: {{.TaskInput}} 