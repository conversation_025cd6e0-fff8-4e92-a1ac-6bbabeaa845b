server:
  port: 8080
  env: "production"  # development, staging, production

auth:
  supabase:
    url: "https://edvmkomjvvmxqhczidgu.supabase.co"
    anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.PPgjOKdZ-CHtthUAceQwZbnh2Fo-FESIKqdSnIKb0t0"  # 可以通过环境变量 SUPABASE_ANON_KEY 设置
    database_url: "postgresql://postgres:[YOUR-PASSWORD]@db.edvmkomjvvmxqhczidgu.supabase.co:5432/postgres"
  cache:
    duration: "5m"
  exclude_paths:
    - "/health"
    - "/metrics"
    - "/swagger"
    - "/proto.store.v1.StoreService/GetPresetPlugins"
    - "/proto.store.v1.StoreService/LoadMorePlugins"
    - "/proto.submission.v1.SubmissionService/SubmitDeeplink"

log:
  level: "info"  # debug, info, warn, error

# Gemini AI 配置示例
ai:
  provider: gemini
  model: gemini-2.5-flash-lite-preview-06-17
  token: your-gemini-api-key-here  # 请替换为你的 Gemini API Key
  format: "json" # json, text

# 使用说明：
# 1. 将此文件复制为 config.yaml
# 2. 替换 your-gemini-api-key-here 为你的实际 Gemini API Key
# 3. 如需使用其他 Gemini 模型，可选择：
#    - gemini-2.5-flash-lite-preview-06-17 (推荐，最新轻量版)
#    - gemini-1.5-pro-latest (功能最强)
#    - gemini-1.5-flash-latest (平衡性能)
#    - gemini-pro (经典版本)
