server:
  port: 8080
  env: "production"

auth:
  supabase:
    url: "https://edvmkomjvvmxqhczidgu.supabase.co"
    anon_key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.PPgjOKdZ-CHtthUAceQwZbnh2Fo-FESIKqdSnIKb0t0"
    database_url: "postgresql://postgres:[YOUR-PASSWORD]@db.edvmkomjvvmxqhczidgu.supabase.co:5432/postgres"
  cache:
    duration: "5m"
  exclude_paths:
    - "/health"
    - "/metrics"
    - "/swagger"
    - "/proto.store.v1.StoreService/GetPresetPlugins"
    - "/proto.store.v1.StoreService/LoadMorePlugins"
    - "/proto.submission.v1.SubmissionService/SubmitDeeplink"
    - "/proto.task.v1.TaskService/ParseVoiceTask"
    - "/proto.update.v1.UpdateService/CheckUpdate"

log:
  level: "info"

ai:
  provider: gemini
  model: gemini-2.5-flash-lite-preview-06-17
  token: AIzaSyA6-gXmH6YofQV92dNiJSwj9Z69wZBgo2k
  format: "json"

stt:
  provider: "google-cloud-stt"  # gemini 或 google-cloud-stt
  gemini:
    model: gemini-2.5-flash-lite-preview-06-17
    token: AIzaSyA6-gXmH6YofQV92dNiJSwj9Z69wZBgo2k
  google_cloud_stt:
    api_key: "AIzaSyAMIcvagR1JI8F0frm2_xFpIV0Kl8b6GWU"
    project_id: "helical-sol-417708"  # 您的实际项目ID
    location: "asia-southeast1"  # 支持中文的区域
    options:
      use_enhanced: true
      automatic_punctuation: true
      model: "latest_long"  # 使用 latest_long 模型，在global位置支持中文
      profanity_filter: false
      enable_word_confidence: false