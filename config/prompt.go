package config

import (
	"bytes"
	_ "embed"
	"fmt"
	"log"
	"strings"
	"text/template"

	"github.com/spf13/viper"
)

//go:embed prompts.yaml
var promptsFileData []byte

var promptConfig *viper.Viper

func init() {
	promptConfig = viper.New()
	promptConfig.SetConfigType("yaml")

	// 首先尝试从嵌入的配置加载
	err := promptConfig.ReadConfig(bytes.NewReader(promptsFileData))
	if err != nil {
		log.Printf("Error reading embedded prompt config: %v", err)

		// 如果嵌入配置加载失败，尝试从文件系统加载
		promptConfig.SetConfigName("prompts")
		promptConfig.AddConfigPath("./config")
		promptConfig.AddConfigPath("/opt/fast/config")

		err = promptConfig.ReadInConfig()
		if err != nil {
			log.Fatalf("Error reading prompt config: %v", err)
		}
	}
}

// GetPrompt 获取指定路径的 prompt
func GetPrompt(path string) string {
	fullPath := fmt.Sprintf("prompts.%s", path)
	if !promptConfig.IsSet(fullPath) {
		log.Printf("Warning: prompt not found at path: %s", fullPath)
		return ""
	}
	return promptConfig.GetString(fullPath)
}

// GetPromptTemplate 获取并渲染指定路径的 prompt 模板
func GetPromptTemplate(path string, data map[string]string) string {
	templateStr := GetPrompt(path)
	if templateStr == "" {
		return ""
	}

	tmpl, err := template.New("prompt").Parse(templateStr)
	if err != nil {
		log.Printf("Error parsing prompt template: %v", err)
		return templateStr
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		log.Printf("Error executing prompt template: %v", err)
		return templateStr
	}

	return strings.TrimSpace(buf.String())
}
