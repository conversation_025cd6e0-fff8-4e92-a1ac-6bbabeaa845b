# Makefile SSH 配置更新

## 🔄 更新内容

### 修改前的配置
```makefile
GCP_REMOTE_USER=ezsenfy
GCP_REMOTE_HOST=************
GCP_DEPLOY_CMD=ssh -n -o ConnectTimeout=10 $(GCP_REMOTE_USER)@$(GCP_REMOTE_HOST)
GCP_TRANSFER_CMD=scp -o ConnectTimeout=10 $(PWD)/$(SERVER_BINARY_NAME) $(GCP_REMOTE_USER)@$(GCP_REMOTE_HOST):$(GCP_REMOTE_PATH)
```

### 修改后的配置
```makefile
GCP_REMOTE_USER=fengyangsen
GCP_REMOTE_HOST=gcp-saien
GCP_DEPLOY_CMD=ssh -n $(GCP_REMOTE_HOST)
GCP_TRANSFER_CMD=scp $(PWD)/$(SERVER_BINARY_NAME) $(GCP_REMOTE_HOST):$(GCP_REMOTE_PATH)
```

## 📋 主要变更

1. **用户名更新**: ezsenfy → fengyangsen
2. **主机配置**: ************ → gcp-saien (使用 SSH 配置别名)
3. **连接方式**: 移除直接 IP 连接，使用 IAP 隧道配置
4. **简化命令**: 移除 ConnectTimeout 参数，依赖 SSH 配置

## 🔧 技术优势

- ✅ **使用 IAP 隧道**: 绕过网络连接问题
- ✅ **ED25519 密钥**: 现代安全的加密算法
- ✅ **SSH 配置管理**: 集中化的连接配置
- ✅ **简化命令**: 更清洁的 Makefile 代码

## 🚀 使用方式

现在您可以使用以下命令进行部署：

```bash
# 构建并部署
make deploy

# 构建、部署并打标签
make deploy_and_tag

# 仅构建 Linux 版本
make build_linux_amd64
```

## 📁 备份

原始 Makefile 已备份为：Makefile.bak

## ⚠️ 注意事项

- 确保 SSH 配置 (~/.ssh/config) 中的 gcp-saien 配置正确
- 确保 ED25519 密钥已正确配置并可访问
- 部署前建议先测试 SSH 连接：ssh gcp-saien whoami
