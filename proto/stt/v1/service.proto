syntax = "proto3";

package proto.stt.v1;

option go_package = "fastserver.com/fastserver/gen/proto/stt/v1";

import "google/protobuf/timestamp.proto";
import "proto/task/v1/service.proto";

// AudioFormat 定义支持的音频格式
enum AudioFormat {
    AUDIO_FORMAT_UNSPECIFIED = 0;
    MP3 = 1;
    WAV = 2;
    M4A = 3;
    OGG = 4;
    FLAC = 5;
    PCM = 6;
}

// TranscribeRequest 定义语音转文本的请求结构
message TranscribeRequest {
    // 音频数据，base64编码
    string audio_data = 1;
    
    // 音频格式
    AudioFormat audio_format = 2;
    
    // 可选：指定使用的模型
    string model = 3;
    
    // 可选：语言代码 (如 "zh-CN", "en-US")
    string language_code = 4;
    
    // 可选：自定义提示词，用于指导转录结果的格式
    string prompt = 5;
    
    // 可选：是否启用任务分析模式（返回结构化的任务信息）
    bool enable_task_analysis = 6;
    
    // 可选：用户已安装的应用列表，用于提升任务解析准确性
    repeated string installed_apps = 7;
}



// TranscribeResponse 定义语音转文本的响应结构
message TranscribeResponse {
    // 转录的文本内容
    string text = 1;

    // 可选：任务分析结果（当 enable_task_analysis 为 true 时）
    proto.task.v1.SimpleTask task_info = 2;

    // 错误信息，如果转录失败则不为空
    string error = 3;

    // Token 使用情况
    proto.task.v1.TokenUsage token_usage = 4;

    // 转录完成时间
    google.protobuf.Timestamp completed_at = 5;

    // 使用的模型名称
    string model_used = 6;

    // 音频时长（秒）
    float audio_duration = 7;
}

// STTService 定义语音转文本相关的服务接口
service STTService {
    // TranscribeAudio 将音频转换为文本
    rpc TranscribeAudio(TranscribeRequest) returns (TranscribeResponse) {}
}
