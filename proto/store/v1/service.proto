syntax = "proto3";

package proto.store.v1;

option go_package = "fastserver.com/fastserver/gen/proto/store/v1";

// Plugin 定义了插件的基本信息
message Plugin {
  string id = 1;                // 插件唯一标识符
  string app_name = 2;          // 应用名称
  string package_name = 3;      // 包名
  string language = 4;          // 语言
  string icon_url = 5;          // 图标URL
  repeated string categories = 6; // 类别列表
}

// Deeplink 定义了深度链接的基本信息
message Deeplink {
  string id = 1;                // 深度链接唯一标识符
  string name = 2;              // 名称
  string deeplink = 3;          // 深度链接URL
  string type = 4;              // 类型 (deeplink 或 action_send)
  bool verified = 5;            // 是否已验证
  bool requires_text_input = 6; // 是否需要文本输入
  string open_with_app = 7;     //  用于 deeplink 支持多 App 时，指定打开的应用
  repeated string preset_groups = 8; // 预设组列表
  string open_with_activity = 9;     // 用于 action_send 某 App 多 Activity 时，指定打开的 Activity
  string config = 10;
}

// PluginDetail 定义了包含深度链接的完整插件信息
message PluginDetail {
  Plugin plugin = 1;            // 插件基本信息
  repeated Deeplink deeplinks = 2; // 插件的深度链接列表
}

// GetPluginRequest 定义了获取单个插件详情的请求
message GetPluginRequest {
  string id = 1;                // 插件ID
}

// GetPluginResponse 定义了获取单个插件详情的响应
message GetPluginResponse {
  PluginDetail plugin = 1;      // 插件详情
}

// GetCategoriesRequest 定义了获取所有类别的请求
message GetCategoriesRequest {}

// GetCategoriesResponse 定义了获取所有类别的响应
message GetCategoriesResponse {
  repeated string categories = 1; // 类别列表
}

// GetPresetGroupsRequest 定义了获取所有预设组的请求
message GetPresetGroupsRequest {}

// GetPresetGroupsResponse 定义了获取所有预设组的响应
message GetPresetGroupsResponse {
  repeated string preset_groups = 1; // 预设组列表
}

// GetPresetPluginsRequest 定义了获取所有预设插件的请求
message GetPresetPluginsRequest {
  string language = 1;  // 可选的语言筛选
}

// GetPresetPluginsResponse 定义了获取所有预设插件的响应
message GetPresetPluginsResponse {
  repeated PluginDetail plugins = 1; // 预设插件列表
}

// LoadMorePluginsRequest 定义了加载更多插件的请求参数
message LoadMorePluginsRequest {
  int32 offset = 1;        // 从第几条开始
  int32 limit = 2;         // 获取多少条
  string category = 3;     // 按类别筛选
  string language = 4;     // 按语言筛选
  string preset_group = 5; // 按预设组筛选
  string search_term = 6;  // 搜索关键词
}

// LoadMorePluginsResponse 定义了加载更多插件的响应
message LoadMorePluginsResponse {
  repeated PluginDetail plugins = 1; // 插件列表
  bool has_more = 2;                 // 是否还有更多数据
  int32 next_offset = 3;             // 下一次请求的起始位置
  int32 total_count = 4;             // 插件总数
}

// StoreService 定义了插件商店相关的服务接口
service StoreService {
  // GetPlugin 获取单个插件的详细信息
  rpc GetPlugin(GetPluginRequest) returns (GetPluginResponse) {}
  
  // GetCategories 获取所有可用的类别
  rpc GetCategories(GetCategoriesRequest) returns (GetCategoriesResponse) {}
  
  // GetPresetGroups 获取所有可用的预设组
  rpc GetPresetGroups(GetPresetGroupsRequest) returns (GetPresetGroupsResponse) {}
  
  // GetPresetPlugins 获取所有预设的插件
  rpc GetPresetPlugins(GetPresetPluginsRequest) returns (GetPresetPluginsResponse) {}
  
  // LoadMorePlugins 加载更多插件，支持筛选
  rpc LoadMorePlugins(LoadMorePluginsRequest) returns (LoadMorePluginsResponse) {}
} 