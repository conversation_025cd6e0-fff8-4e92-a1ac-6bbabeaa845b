syntax = "proto3";

package proto.store.v2;

option go_package = "fastserver.com/fastserver/gen/proto/store/v2";

// AppBrief 定义了应用的基本信息
message AppBrief {
  string app_id = 1;            // 应用唯一标识符
  string app_name = 2;          // 应用名称
  string package_name = 3;      // 包名
  string language = 4;          // 语言
  string icon_url = 5;          // 图标URL
  repeated string categories = 6; // 类别列表
}

// PluginBrief 定义了插件的基本信息
message PluginBrief {
  string plugin_id = 1;                // 插件唯一标识符
  string plugin_name = 2;              // 名称       // 深度链接URL
  string plugin_type = 3;              // 类型 (deeplink 或 action_send)
  repeated string preset_groups = 4; // 预设组列表
  string plugin_icon_url = 5;          // 图标URL
  string plugin_description = 6;       // 描述
}

// Based on the Kotlin LocalPlugin sealed class
message Plugin {
  string plugin_id = 1;
  string plugin_name = 2;
  string app_name = 3;
  string app_package_name = 4; // package name for applications
  repeated string categories = 5;
  optional string icon_url = 6;

  oneof plugin_type {
    OpenAppPlugin open_app_plugin = 7;
    DeeplinkPlugin deeplink_plugin = 8;
    ActionSendPlugin action_send_plugin = 9;
    ProcessTextPlugin process_text_plugin = 10;
    WebhookPlugin webhook_plugin = 11;
  }
}

message OpenAppPlugin {
  // No unique params, common fields are in Plugin message
}

message DeeplinkPlugin {
  string deeplink = 1;
  string replacement = 2;
  optional string specified_app_package_name = 3; // force open with a app (empty for system choosing)
}

message ActionSendPlugin {
  optional string specified_component = 1; // when calling action_send, passing component if this field exist -- it's a activity class path
}

message ProcessTextPlugin {
  optional string specified_component = 1; // when calling action_process_text, passing component if this field exist -- it's a activity class path
}

message WebhookPlugin {
  string webhook_config = 1;
}
