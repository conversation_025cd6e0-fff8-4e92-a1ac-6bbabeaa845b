syntax = "proto3";

package proto.task.v1;

option go_package = "fastserver.com/fastserver/gen/proto/task/v1";

import "google/protobuf/timestamp.proto";

enum RepeatIntervalUnit {
    REPEAT_INTERVAL_UNIT_UNSPECIFIED = 0;
    DAY = 1;
    WEEK = 2;
    MONTH = 3;
    YEAR = 4;
}

// AudioFormat 定义支持的音频格式
enum AudioFormat {
    AUDIO_FORMAT_UNSPECIFIED = 0;
    MP3 = 1;
    WAV = 2;
    M4A = 3;
    OGG = 4;
    FLAC = 5;
    PCM = 6;
}

// SimpleTask 定义了一个基础任务的结构
message SimpleTask {
    string id = 1;           // 任务唯一标识符
    string name = 2;         // 任务名称
    string plugin = 3;       // 插件名称（对应的APP）
    string search = 4;       // 搜索内容
    google.protobuf.Timestamp start_time = 5;  // 首次执行时间
    bool repeatable = 6;         // 是否为重复任务
    int32 repeat_times = 7;      // 重复次数
    int32 repeat_interval_value = 8;     // 重复周期值
    RepeatIntervalUnit repeat_interval_unit = 9;     // 重复周期单位
    string description = 10;  // 任务描述
    string timezone = 11;    // 时区信息
}

// TokenUsage 定义了 token 使用情况的详细结构
message TokenUsage {
    int32 prompt_tokens = 1;      // 提示词使用的 token 数量
    int32 completion_tokens = 2;  // 完成使用的 token 数量
    int32 total_tokens = 3;       // 总 token 数量
}

// AnalyzeRequest 定义了分析任务的请求结构
message AnalyzeRequest {
    string input = 1;        // 用户输入的任务描述文本
}

// AnalyzeResponse 定义了分析任务的响应结构
message AnalyzeResponse {
    SimpleTask task = 1;     // 解析后的任务信息
    string methods = 2;      // 解析方法
    string error = 3;        // 错误信息，如果解析失败则不为空
    TokenUsage detailed_token_usage = 4; // 详细的 token 使用情况
}

// ParseVoiceTaskRequest 定义语音任务解析的请求结构
message ParseVoiceTaskRequest {
    // 音频数据，base64编码
    string audio_data = 1;
    
    // 音频格式
    AudioFormat audio_format = 2;
    
    // 可选：语言代码 (如 "zh-CN", "en-US")
    string language_code = 3;
    
    // 可选：用户已安装的应用列表，用于提升任务解析准确性
    repeated string installed_apps = 4;
}

// ParseVoiceTaskResponse 定义语音任务解析的响应结构
message ParseVoiceTaskResponse {
    // 解析的任务信息
    SimpleTask task = 1;
    
    // 转录的原始文本
    string transcribed_text = 2;
    
    // 错误信息，如果解析失败则不为空
    string error = 3;
    
    // Token 使用情况
    TokenUsage token_usage = 4;
    
    // 解析完成时间
    google.protobuf.Timestamp completed_at = 5;
    
    // 使用的模型名称
    string model_used = 6;
    
    // 音频时长（秒）
    float audio_duration = 7;
}

// TaskService 定义了任务相关的服务接口
service TaskService {
    // Analyze 解析用户输入的任务描述，返回结构化的任务信息
    rpc Analyze(AnalyzeRequest) returns (AnalyzeResponse) {}
    
    // ParseVoiceTask 解析用户语音中的任务信息
    rpc ParseVoiceTask(ParseVoiceTaskRequest) returns (ParseVoiceTaskResponse) {}
} 