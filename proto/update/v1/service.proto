syntax = "proto3";

package proto.update.v1;

option go_package = "fastserver.com/fastserver/gen/proto/update/v1";

// CheckUpdateRequest 定义了检查更新的请求结构
message CheckUpdateRequest {
  // 请求体为空，版本信息从请求头获取
}

// CheckUpdateResponse 定义了检查更新的响应结构
message CheckUpdateResponse {
  bool has_update = 1;           // 是否有更新可用
  bool force_update = 2;         // 是否强制更新
  string current_version = 3;    // 当前版本
  string latest_version = 4;     // 最新版本
  string release_note = 5;       // 更新说明
}

// UpdateService 定义了更新相关的服务接口
service UpdateService {
  // CheckUpdate 检查是否有新版本可用
  rpc CheckUpdate(CheckUpdateRequest) returns (CheckUpdateResponse) {}
} 