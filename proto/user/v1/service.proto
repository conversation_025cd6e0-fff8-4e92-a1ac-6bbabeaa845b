syntax = "proto3";

package proto.user.v1;

option go_package = "fastserver.com/fastserver/gen/proto/user/v1";

message User {
    string id = 1;
    string name = 2;
    string email = 3;
    string avatar = 4;
    string created_at = 5;
    string updated_at = 6;
    optional string firebase_uid = 7;
}

message GetUserRequest {
    oneof identifier {
        string id = 1;
        string firebase_uid = 2;
    }
}

message GetUserResponse {
    User user = 1;
}

message LoginRequest {
    string firebase_uid = 1;
    string email = 2;
}

message LoginResponse {
}

service UserService {
    rpc GetUser(GetUserRequest) returns (GetUserResponse);
    rpc Login(LoginRequest) returns (LoginResponse);
}
