syntax = "proto3";

package proto.submission.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto"; // For optional fields

option go_package = "fastserver.com/fastserver/gen/proto/submission/v1;submissionv1";

// Service for handling deeplink submissions
service SubmissionService {
  // Submits a new deeplink for review.
  rpc SubmitDeeplink(SubmitDeeplinkRequest) returns (SubmitDeeplinkResponse);
}

// Represents a submitted deeplink record (mirrors store.SubmittedDeeplink)
message SubmittedDeeplink {
  int64 id = 1;
  string user_id = 2; // UUID as string
  google.protobuf.StringValue deeplink = 3;
  google.protobuf.StringValue name = 4;
  google.protobuf.StringValue type = 5;
  google.protobuf.BoolValue requires_text_input = 6;
  google.protobuf.StringValue open_with_app = 7;
  google.protobuf.StringValue open_with_activity = 8;
  google.protobuf.StringValue status = 9;
  google.protobuf.StringValue app_package_name = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
}


// Request message for submitting a deeplink
message SubmitDeeplinkRequest {
  // Required: The deeplink URL itself.
  string deeplink = 1;
  // Required: A user-friendly name for the deeplink.
  string name = 2;
   // Required: The type of the deeplink (e.g., 'activity', 'service', 'broadcast').
  string type = 3;
  // Required: Package name of the target app.
  string app_package_name = 4;
  // Optional: Does this deeplink require text input from the user?
  optional bool requires_text_input = 5;
  // Optional: Specific app to open with (if different from app_package_name, e.g., chooser).
  optional string open_with_app = 6;
   // Optional: Specific activity component to target within the app.
  optional string open_with_activity = 7;
}

// Response message after submitting a deeplink
message SubmitDeeplinkResponse {
  // The details of the submitted deeplink record created.
  SubmittedDeeplink submitted_deeplink = 1;
} 