# 大模型抽象层

这个包提供了一个统一的大模型接口层，支持多种大模型提供商的实现，包括 OpenAI、Gemini 等。

## 设计模式

这个实现采用了经典的**策略模式**和**工厂模式**：

1. **LLMProvider 接口**：定义了所有大模型提供商必须实现的方法
2. **具体实现**：OpenAIProvider、GeminiProvider 等实现了 LLMProvider 接口  
3. **LLMManager**：管理多个提供商，支持动态切换和故障转移

## 核心接口

```go
type LLMProvider interface {
    GetName() string
    Completion(ctx context.Context, req CompletionRequest) (*CompletionResponse, error)
    SimpleCompletion(ctx context.Context, system, user string) (string, TokenUsage, error)
    IsAvailable(ctx context.Context) bool
    GetModels() []string
    GetDefaultModel() string
}
```

## 使用示例

### 基本使用

```go
package main

import (
    "context"
    "fmt"
    "log"
    
    "fastserver.com/fastserver/ai"
    "fastserver.com/fastserver/ai/impl"
)

func main() {
    // 创建管理器
    manager := ai.NewLLMManager()

    // 注册提供商
    openaiProvider := impl.NewOpenAIProvider("your-openai-api-key")
    manager.RegisterProvider("openai", openaiProvider)

    geminiProvider := impl.NewGeminiProvider("your-gemini-api-key")
    manager.RegisterProvider("gemini", geminiProvider)

    // 设置默认提供商
    manager.SetDefault("openai")

    ctx := context.Background()

    // 简单对话
    response, tokenUsage, err := manager.SimpleCompletion(ctx, 
        "你是一个有用的助手", 
        "请介绍一下Go语言")
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("回复: %s\n", response)
    fmt.Printf("Token使用: %+v\n", tokenUsage)
}
```

### 使用指定提供商

```go
// 获取特定提供商
gemini, err := manager.GetProvider("gemini")
if err != nil {
    log.Fatal(err)
}

response, tokenUsage, err := gemini.SimpleCompletion(ctx,
    "你是一个编程专家",
    "解释一下Go的接口特性")
```

### 完整对话

```go
req := ai.CompletionRequest{
    Messages: []ai.Message{
        {Role: "system", Content: "你是一个代码审查专家"},
        {Role: "user", Content: "请审查这段Go代码"},
        {Role: "assistant", Content: "好的，请提供代码"},
        {Role: "user", Content: "type LLMProvider interface { GetName() string }"},
    },
    MaxTokens:   1000,
    Temperature: 0.7,
    Model:       "gpt-4",
}

response, err := manager.Completion(ctx, req)
```

### 故障转移

```go
func ProviderFailover(manager *ai.LLMManager, ctx context.Context, system, user string) (string, ai.TokenUsage, error) {
    providers := manager.ListProviders()
    
    for _, providerName := range providers {
        provider, err := manager.GetProvider(providerName)
        if err != nil {
            continue
        }

        if !provider.IsAvailable(ctx) {
            fmt.Printf("提供商 %s 不可用\n", providerName)
            continue
        }

        response, tokenUsage, err := provider.SimpleCompletion(ctx, system, user)
        if err == nil {
            return response, tokenUsage, nil
        }
    }

    return "", ai.TokenUsage{}, fmt.Errorf("所有提供商都不可用")
}
```

## 支持的提供商

### OpenAI

```go
// 标准 OpenAI
openaiProvider := impl.NewOpenAIProvider("your-api-key")

// 自定义 baseURL（如使用代理或其他兼容服务）
openaiProvider := impl.NewOpenAIProviderWithBaseURL("your-api-key", "https://api.openai.com/v1")
```

支持的模型：
- gpt-4
- gpt-4-turbo  
- gpt-3.5-turbo
- gpt-3.5-turbo-16k

### Gemini

```go
geminiProvider := impl.NewGeminiProvider("your-api-key")
```

支持的模型：
- gemini-1.5-pro-latest
- gemini-1.5-flash-latest
- gemini-pro

## 扩展新的提供商

要添加新的大模型提供商，只需实现 `LLMProvider` 接口：

```go
type MyProvider struct {
    apiKey string
    // 其他字段...
}

func (p *MyProvider) GetName() string {
    return "my-provider"
}

func (p *MyProvider) Completion(ctx context.Context, req ai.CompletionRequest) (*ai.CompletionResponse, error) {
    // 实现具体的API调用逻辑
    // ...
}

func (p *MyProvider) SimpleCompletion(ctx context.Context, system, user string) (string, ai.TokenUsage, error) {
    // 实现简单对话逻辑
    // ...
}

// 实现其他接口方法...
```

然后注册到管理器：

```go
myProvider := &MyProvider{apiKey: "your-key"}
manager.RegisterProvider("my-provider", myProvider)
```

## 最佳实践

1. **错误处理**：始终检查API调用的错误，考虑实现重试机制
2. **超时控制**：在context中设置合适的超时时间
3. **并发安全**：LLMManager是并发安全的，可以在多个goroutine中使用
4. **资源管理**：及时释放HTTP连接，避免内存泄漏
5. **监控和日志**：记录API调用情况，监控token使用量和响应时间

这种设计的优势：
- **可扩展性**：易于添加新的大模型提供商
- **灵活性**：可以动态切换提供商，支持故障转移
- **一致性**：统一的接口，减少业务代码的复杂性
- **可测试性**：易于mock和单元测试 