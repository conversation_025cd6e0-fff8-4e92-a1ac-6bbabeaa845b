package ai

import (
	"context"
	"fmt"
)

// TokenUsage 表示 token 使用情况
type TokenUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// Message 表示对话消息
type Message struct {
	Role    string `json:"role"` // "system", "user", "assistant"
	Content string `json:"content"`
}

// CompletionRequest 表示完成请求
type CompletionRequest struct {
	Messages    []Message `json:"messages"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Temperature float64   `json:"temperature,omitempty"`
	Model       string    `json:"model,omitempty"`
}

// CompletionResponse 表示完成响应
type CompletionResponse struct {
	Content    string     `json:"content"`
	TokenUsage TokenUsage `json:"token_usage"`
}

// STTRequest 表示语音转文本请求
type STTRequest struct {
	AudioData    []byte `json:"audio_data"` // 音频数据
	MimeType     string `json:"mime_type"`  // MIME 类型，如 "audio/mp3", "audio/wav"
	Model        string `json:"model,omitempty"`
	LanguageCode string `json:"language_code,omitempty"`
	Prompt       string `json:"prompt,omitempty"` // 自定义提示词
}

// STTResponse 表示语音转文本响应
type STTResponse struct {
	Text          string     `json:"text"`
	TokenUsage    TokenUsage `json:"token_usage"`
	AudioDuration float64    `json:"audio_duration,omitempty"` // 音频时长（秒）
}

// LLMProvider 定义大模型提供商的接口
type LLMProvider interface {
	// GetName 返回提供商名称
	GetName() string

	// Completion 执行文本完成
	Completion(ctx context.Context, req CompletionRequest) (*CompletionResponse, error)

	// SimpleCompletion 简单的完成接口，包装了系统和用户消息
	SimpleCompletion(ctx context.Context, system, user string) (string, TokenUsage, error)

	// IsAvailable 检查服务是否可用
	IsAvailable(ctx context.Context) bool

	// GetModels 获取支持的模型列表
	GetModels() []string

	// GetDefaultModel 获取默认模型
	GetDefaultModel() string
}

// STTProvider 定义语音转文本提供商的接口
type STTProvider interface {
	// GetName 返回提供商名称
	GetName() string

	// TranscribeAudio 执行语音转文本
	TranscribeAudio(ctx context.Context, req STTRequest) (*STTResponse, error)

	// IsAvailable 检查服务是否可用
	IsAvailable(ctx context.Context) bool

	// GetSupportedFormats 获取支持的音频格式
	GetSupportedFormats() []string
}

// LLMManager 管理多个大模型提供商
type LLMManager struct {
	providers   map[string]LLMProvider
	defaultName string
}

// NewLLMManager 创建新的大模型管理器
func NewLLMManager() *LLMManager {
	return &LLMManager{
		providers: make(map[string]LLMProvider),
	}
}

// RegisterProvider 注册大模型提供商
func (m *LLMManager) RegisterProvider(name string, provider LLMProvider) {
	m.providers[name] = provider
	if m.defaultName == "" {
		m.defaultName = name
	}
}

// SetDefault 设置默认提供商
func (m *LLMManager) SetDefault(name string) error {
	if _, exists := m.providers[name]; !exists {
		return fmt.Errorf("provider %s not found", name)
	}
	m.defaultName = name
	return nil
}

// GetProvider 获取指定提供商
func (m *LLMManager) GetProvider(name string) (LLMProvider, error) {
	provider, exists := m.providers[name]
	if !exists {
		return nil, fmt.Errorf("provider %s not found", name)
	}
	return provider, nil
}

// GetDefaultProvider 获取默认提供商
func (m *LLMManager) GetDefaultProvider() (LLMProvider, error) {
	if m.defaultName == "" {
		return nil, fmt.Errorf("no default provider set")
	}
	return m.GetProvider(m.defaultName)
}

// Completion 使用默认提供商执行完成
func (m *LLMManager) Completion(ctx context.Context, req CompletionRequest) (*CompletionResponse, error) {
	provider, err := m.GetDefaultProvider()
	if err != nil {
		return nil, err
	}
	return provider.Completion(ctx, req)
}

// SimpleCompletion 使用默认提供商执行简单完成
func (m *LLMManager) SimpleCompletion(ctx context.Context, system, user string) (string, TokenUsage, error) {
	provider, err := m.GetDefaultProvider()
	if err != nil {
		return "", TokenUsage{}, err
	}
	return provider.SimpleCompletion(ctx, system, user)
}

// ListProviders 列出所有已注册的提供商
func (m *LLMManager) ListProviders() []string {
	var names []string
	for name := range m.providers {
		names = append(names, name)
	}
	return names
}
