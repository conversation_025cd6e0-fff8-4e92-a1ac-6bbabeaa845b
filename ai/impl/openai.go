package impl

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"fastserver.com/fastserver/ai"
)

// OpenAIProvider OpenAI 实现
type OpenAIProvider struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	models     []string
}

// OpenAIRequest OpenAI API 请求格式
type OpenAIRequest struct {
	Model       string       `json:"model"`
	Messages    []ai.Message `json:"messages"`
	MaxTokens   int          `json:"max_tokens,omitempty"`
	Temperature float64      `json:"temperature,omitempty"`
}

// OpenAIResponse OpenAI API 响应格式
type OpenAIResponse struct {
	Choices []struct {
		Message ai.Message `json:"message"`
	} `json:"choices"`
	Usage ai.TokenUsage `json:"usage"`
}

// NewOpenAIProvider 创建新的 OpenAI 提供商
func NewOpenAIProvider(apiKey string) *OpenAIProvider {
	return &OpenAIProvider{
		apiKey:  apiKey,
		baseURL: "https://api.openai.com/v1",
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		models: []string{
			"gpt-4",
			"gpt-4-turbo",
			"gpt-3.5-turbo",
			"gpt-3.5-turbo-16k",
		},
	}
}

// NewOpenAIProviderWithBaseURL 创建带自定义 baseURL 的 OpenAI 提供商
func NewOpenAIProviderWithBaseURL(apiKey, baseURL string) *OpenAIProvider {
	provider := NewOpenAIProvider(apiKey)
	provider.baseURL = baseURL
	return provider
}

// GetName 实现 LLMProvider 接口
func (p *OpenAIProvider) GetName() string {
	return "openai"
}

// Completion 实现 LLMProvider 接口
func (p *OpenAIProvider) Completion(ctx context.Context, req ai.CompletionRequest) (*ai.CompletionResponse, error) {
	// 设置默认模型
	model := req.Model
	if model == "" {
		model = p.GetDefaultModel()
	}

	// 构建 OpenAI 请求
	openaiReq := OpenAIRequest{
		Model:       model,
		Messages:    req.Messages,
		MaxTokens:   req.MaxTokens,
		Temperature: req.Temperature,
	}

	// 序列化请求
	reqBody, err := json.Marshal(openaiReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/chat/completions", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+p.apiKey)

	// 发送请求
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var openaiResp OpenAIResponse
	if err := json.Unmarshal(respBody, &openaiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查响应是否有效
	if len(openaiResp.Choices) == 0 {
		return nil, fmt.Errorf("no choices in response")
	}

	return &ai.CompletionResponse{
		Content:    openaiResp.Choices[0].Message.Content,
		TokenUsage: openaiResp.Usage,
	}, nil
}

// SimpleCompletion 实现 LLMProvider 接口
func (p *OpenAIProvider) SimpleCompletion(ctx context.Context, system, user string) (string, ai.TokenUsage, error) {
	messages := []ai.Message{
		{Role: "system", Content: system},
		{Role: "user", Content: user},
	}

	req := ai.CompletionRequest{
		Messages: messages,
	}

	resp, err := p.Completion(ctx, req)
	if err != nil {
		return "", ai.TokenUsage{}, err
	}

	return resp.Content, resp.TokenUsage, nil
}

// IsAvailable 实现 LLMProvider 接口
func (p *OpenAIProvider) IsAvailable(ctx context.Context) bool {
	// 创建一个简单的测试请求
	req, err := http.NewRequestWithContext(ctx, "GET", p.baseURL+"/models", nil)
	if err != nil {
		return false
	}

	req.Header.Set("Authorization", "Bearer "+p.apiKey)

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// GetModels 实现 LLMProvider 接口
func (p *OpenAIProvider) GetModels() []string {
	return p.models
}

// GetDefaultModel 实现 LLMProvider 接口
func (p *OpenAIProvider) GetDefaultModel() string {
	if len(p.models) > 0 {
		return p.models[0]
	}
	return "gpt-3.5-turbo"
}
