package impl

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os/exec"
	"regexp"
	"strings"

	"fastserver.com/fastserver/ai"
	"fastserver.com/fastserver/utils/logger"
	"golang.org/x/oauth2/google"
)

// GoogleCloudSTTProvider Google Cloud Speech-to-Text实现
type GoogleCloudSTTProvider struct {
	apiKey     string
	projectID  string
	httpClient *http.Client
	baseURL    string
	options    *GoogleCloudSTTOptions
}

// GoogleCloudSTTOptions Google Cloud STT配置选项
type GoogleCloudSTTOptions struct {
	UseEnhanced          bool
	AutomaticPunctuation bool
	Model                string
	ProfanityFilter      bool
	EnableWordConfidence bool
}

// NewGoogleCloudSTTProvider 创建Google Cloud STT提供商
func NewGoogleCloudSTTProvider(apiKey, projectID string, options *GoogleCloudSTTOptions) *GoogleCloudSTTProvider {
	// 设置默认选项
	if options == nil {
		options = &GoogleCloudSTTOptions{
			UseEnhanced:          true,
			AutomaticPunctuation: true,
			Model:                "latest_long",
			ProfanityFilter:      false,
			EnableWordConfidence: false,
		}
	}

	return &GoogleCloudSTTProvider{
		apiKey:     apiKey,
		projectID:  projectID,
		httpClient: &http.Client{},
		baseURL:    "https://speech.googleapis.com/v2",
		options:    options,
	}
}

// Google Cloud Speech-to-Text V2 API 结构体定义

// AutoDetectDecodingConfig 自动检测解码配置
type AutoDetectDecodingConfig struct{}

// RecognitionConfig V2 识别配置
type RecognitionConfig struct {
	AutoDecodingConfig *AutoDetectDecodingConfig `json:"autoDecodingConfig,omitempty"`
	LanguageCodes      []string                  `json:"languageCodes"`
	Model              string                    `json:"model,omitempty"`
	Features           *RecognitionFeatures      `json:"features,omitempty"`
}

// RecognitionFeatures 识别特性
type RecognitionFeatures struct {
	EnableAutomaticPunctuation bool `json:"enableAutomaticPunctuation,omitempty"`
	EnableWordTimeOffsets      bool `json:"enableWordTimeOffsets,omitempty"`
	EnableWordConfidence       bool `json:"enableWordConfidence,omitempty"`
	EnableSpokenPunctuation    bool `json:"enableSpokenPunctuation,omitempty"`
	EnableSpokenEmojis         bool `json:"enableSpokenEmojis,omitempty"`
	ProfanityFilter            bool `json:"profanityFilter,omitempty"`
	MaxAlternatives            int  `json:"maxAlternatives,omitempty"`
}

// RecognizeRequest V2 识别请求
type RecognizeRequest struct {
	Recognizer string             `json:"recognizer"`
	Config     *RecognitionConfig `json:"config"`
	Content    string             `json:"content"` // base64编码的音频数据
}

// WordInfo V2 单词信息
type WordInfo struct {
	StartOffset string  `json:"startOffset"`
	EndOffset   string  `json:"endOffset"`
	Word        string  `json:"word"`
	Confidence  float64 `json:"confidence,omitempty"`
}

// SpeechRecognitionAlternative V2 识别结果候选
type SpeechRecognitionAlternative struct {
	Transcript string     `json:"transcript"`
	Confidence float64    `json:"confidence"`
	Words      []WordInfo `json:"words,omitempty"`
}

// SpeechRecognitionResult V2 识别结果
type SpeechRecognitionResult struct {
	Alternatives []SpeechRecognitionAlternative `json:"alternatives"`
	ChannelTag   int                            `json:"channelTag,omitempty"`
	LanguageCode string                         `json:"languageCode,omitempty"`
}

// RecognizeResponse V2 识别响应
type RecognizeResponse struct {
	Results []SpeechRecognitionResult `json:"results"`
	Error   *APIError                 `json:"error,omitempty"`
}

// APIError API错误信息
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// GetName 返回提供商名称
func (p *GoogleCloudSTTProvider) GetName() string {
	return "google-cloud-stt"
}

// TranscribeAudio 执行语音转文本（使用Google Cloud Speech-to-Text V2 API）
func (p *GoogleCloudSTTProvider) TranscribeAudio(ctx context.Context, req ai.STTRequest) (*ai.STTResponse, error) {
	// 将音频数据编码为base64
	audioBase64 := base64.StdEncoding.EncodeToString(req.AudioData)

	// 处理和验证语言代码
	languageCode := p.normalizeLanguageCode(req.LanguageCode)

	// 构建识别配置（使用V2 API的auto_decoding_config）
	config := &RecognitionConfig{
		AutoDecodingConfig: &AutoDetectDecodingConfig{}, // 自动检测音频格式，支持M4A
		LanguageCodes:      []string{languageCode},
	}

	// 设置模型
	if p.options.Model != "" && p.options.Model != "default" {
		config.Model = p.options.Model
	}

	// 设置特性
	if p.options.AutomaticPunctuation || p.options.EnableWordConfidence || p.options.ProfanityFilter {
		config.Features = &RecognitionFeatures{
			EnableAutomaticPunctuation: p.options.AutomaticPunctuation,
			EnableWordTimeOffsets:      false, // 不需要词级时间戳
			EnableWordConfidence:       p.options.EnableWordConfidence,
			ProfanityFilter:            p.options.ProfanityFilter,
			MaxAlternatives:            1, // 只返回最佳结果
		}
	}

	// 构建请求（使用V2 API的recognizer格式，需要真实的项目ID）
	recognizer := fmt.Sprintf("projects/%s/locations/global/recognizers/_", p.projectID)

	speechReq := &RecognizeRequest{
		Recognizer: recognizer,
		Config:     config,
		Content:    audioBase64,
	}

	// 序列化请求
	reqBody, err := json.Marshal(speechReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal speech request: %w", err)
	}

	// 使用Service Account Impersonation而不是Application Default Credentials
	// 这样可以利用现有的gcloud认证
	ctx = context.Background()

	// 尝试获取默认凭据，如果失败则使用gcloud命令获取token
	var accessToken string

	// 首先尝试Application Default Credentials
	creds, err := google.FindDefaultCredentials(ctx, "https://www.googleapis.com/auth/cloud-platform")
	if err == nil {
		token, err := creds.TokenSource.Token()
		if err == nil {
			accessToken = token.AccessToken
		}
	}

	// 如果ADC不可用，则使用gcloud命令获取token
	if accessToken == "" {
		cmd := exec.Command("gcloud", "auth", "print-access-token")
		output, err := cmd.Output()
		if err != nil {
			return nil, fmt.Errorf("failed to get access token from gcloud: %w", err)
		}
		accessToken = strings.TrimSpace(string(output))
	}

	if accessToken == "" {
		return nil, fmt.Errorf("no access token available, please run 'gcloud auth login'")
	}

	// 构建URL（V2 API使用OAuth认证，不需要API Key）
	url := fmt.Sprintf("%s/%s:recognize", p.baseURL, recognizer)

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create speech request: %w", err)
	}

	// 设置请求头（使用OAuth Bearer Token）
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	// 发送请求
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send speech request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read speech response: %w", err)
	}

	// 如果状态码不是200，返回错误
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Speech V2 API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var speechResp RecognizeResponse
	if err := json.Unmarshal(body, &speechResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal speech response: %w", err)
	}

	// 检查API错误
	if speechResp.Error != nil {
		return nil, fmt.Errorf("Speech V2 API error: %s", speechResp.Error.Message)
	}

	// 处理响应
	if len(speechResp.Results) == 0 {
		return &ai.STTResponse{
			Text:       "",
			TokenUsage: ai.TokenUsage{}, // Speech-to-Text不使用token计费
		}, nil
	}

	// 获取第一个结果的第一个候选
	result := speechResp.Results[0]
	if len(result.Alternatives) == 0 {
		return &ai.STTResponse{
			Text:       "",
			TokenUsage: ai.TokenUsage{}, // Speech-to-Text不使用token计费
		}, nil
	}

	alternative := result.Alternatives[0]

	return &ai.STTResponse{
		Text:       alternative.Transcript,
		TokenUsage: ai.TokenUsage{}, // Speech-to-Text不使用token计费
	}, nil
}

// IsAvailable 检查Google Cloud STT是否可用
func (p *GoogleCloudSTTProvider) IsAvailable(ctx context.Context) bool {
	return p.apiKey != ""
}

// GetSupportedFormats 返回支持的音频格式
func (p *GoogleCloudSTTProvider) GetSupportedFormats() []string {
	return []string{
		"audio/wav",
		"audio/flac",
		"audio/mp3",
		"audio/mpeg",
		"audio/ogg",
		"audio/webm",
		"audio/m4a", // V2 API支持M4A格式
		"audio/aac", // V2 API支持AAC格式
		"audio/mp4", // V2 API支持MP4音频
	}
}

// normalizeLanguageCode 规范化和验证语言代码，确保符合BCP-47标准
func (p *GoogleCloudSTTProvider) normalizeLanguageCode(languageCode string) string {
	// 如果没有提供语言代码，使用中文简体作为默认值 (V2 API格式)
	if languageCode == "" {
		return "cmn-Hans-CN"
	}

	// 语言代码映射表，将常见的不规范格式映射为V2 API标准格式
	languageMap := map[string]string{
		// 中文简体变体 (V2 API格式)
		"zh":          "cmn-Hans-CN",
		"zh-cn":       "cmn-Hans-CN",
		"zh-CN":       "cmn-Hans-CN",
		"chinese":     "cmn-Hans-CN",
		"mandarin":    "cmn-Hans-CN",
		"cmn-hans":    "cmn-Hans-CN",
		"cmn-hans-cn": "cmn-Hans-CN",

		// 中文繁体
		"zh-tw":       "zh-TW",
		"zh-hk":       "zh-HK",
		"cmn-hant":    "cmn-Hant-TW",
		"cmn-hant-tw": "cmn-Hant-TW",

		// 粤语
		"yue":       "yue-Hant-HK",
		"cantonese": "yue-Hant-HK",

		// 英语
		"en":      "en-US",
		"english": "en-US",

		// 日语
		"ja":       "ja-JP",
		"japanese": "ja-JP",

		// 韩语
		"ko":     "ko-KR",
		"korean": "ko-KR",

		// 其他常见语言
		"fr":         "fr-FR",
		"french":     "fr-FR",
		"de":         "de-DE",
		"german":     "de-DE",
		"es":         "es-ES",
		"spanish":    "es-ES",
		"it":         "it-IT",
		"italian":    "it-IT",
		"pt":         "pt-BR",
		"portuguese": "pt-BR",
		"ru":         "ru-RU",
		"russian":    "ru-RU",
		"ar":         "ar-SA",
		"arabic":     "ar-SA",
		"hi":         "hi-IN",
		"hindi":      "hi-IN",
	}

	// 转换为小写进行匹配
	lowerCode := strings.ToLower(languageCode)

	// 检查是否在映射表中
	if standardCode, exists := languageMap[lowerCode]; exists {
		return standardCode
	}

	// 如果不在映射表中，验证是否为有效的BCP-47格式
	if p.isValidBCP47(languageCode) {
		return languageCode
	}

	// 如果都不匹配，输出警告但仍然返回原始值，让API自己处理
	logger.Warn("⚠️ 无法识别的语言代码: %s，将使用原始值", languageCode)
	return languageCode
}

// isValidBCP47 检查是否为有效的BCP-47语言标识符
func (p *GoogleCloudSTTProvider) isValidBCP47(languageCode string) bool {
	// 简单的BCP-47验证正则表达式
	// 格式: language[-script][-region]
	// 例如: en, en-US, zh-CN, cmn-Hans-CN
	bcp47Regex := regexp.MustCompile(`^[a-z]{2,3}(-[A-Z][a-z]{3})?(-[A-Z]{2})?$`)
	return bcp47Regex.MatchString(languageCode)
}
