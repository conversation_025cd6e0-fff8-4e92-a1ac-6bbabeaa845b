package impl

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"fastserver.com/fastserver/utils/logger"
)

// APIKey 是 StepFun API 的密钥
const APIKey = "4AqjTuCzlYyl6rzqPY4lMIK5ZeMRL6DKne9nMHhMNtunfyrHueUttz2lKKe5IfJ0h"
const APIUrl = "https://api.stepfun.com/v1/chat/completions"
const Model = "step-2-mini"

// TokenUsage 记录 token 使用情况
type TokenUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// StepFunCompletion 向 StepFun API发送请求并获取结果
func StepFunCompletion(
	systemPrompt string,
	userPrompt string,
) (string, TokenUsage, error) {
	// 准备请求体
	requestBody, err := json.Marshal(map[string]interface{}{
		"model": Model,
		"messages": []map[string]string{
			{
				"role":    "system",
				"content": systemPrompt,
			},
			{
				"role":    "user",
				"content": userPrompt,
			},
		},
	})
	if err != nil {
		return "", TokenUsage{}, fmt.Errorf("error marshaling request body: %v", err)
	}

	// 打印格式化的请求体
	var prettyRequest bytes.Buffer
	if err := json.Indent(&prettyRequest, requestBody, "", "  "); err == nil {
		logger.Debug("StepFun API Request:\n%s", prettyRequest.String())
	} else {
		logger.Debug("StepFun API Request (raw): %s", string(requestBody))
	}

	// 创建请求
	req, err := http.NewRequest("POST", APIUrl, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", TokenUsage{}, fmt.Errorf("error creating request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+APIKey)
	logger.Debug("StepFun API URL: %s", APIUrl)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", TokenUsage{}, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", TokenUsage{}, fmt.Errorf("error reading response: %v", err)
	}

	// 打印格式化的响应体
	var prettyResponse bytes.Buffer
	if err := json.Indent(&prettyResponse, body, "", "  "); err == nil {
		logger.Debug("StepFun API Response:\n%s", prettyResponse.String())
	} else {
		logger.Debug("StepFun API Response (raw): %s", string(body))
	}

	// 解析响应
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", TokenUsage{}, fmt.Errorf("error unmarshaling response: %v", err)
	}

	// 提取生成的文本
	choices, ok := result["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return "", TokenUsage{}, fmt.Errorf("unexpected response format")
	}
	message, ok := choices[0].(map[string]interface{})["message"].(map[string]interface{})
	if !ok {
		return "", TokenUsage{}, fmt.Errorf("unexpected response format")
	}
	content, ok := message["content"].(string)
	if !ok {
		return "", TokenUsage{}, fmt.Errorf("unexpected response format")
	}

	// 提取 token 使用情况
	var tokenUsage TokenUsage
	if usage, ok := result["usage"].(map[string]interface{}); ok {
		if promptTokens, ok := usage["prompt_tokens"].(float64); ok {
			tokenUsage.PromptTokens = int(promptTokens)
		}
		if completionTokens, ok := usage["completion_tokens"].(float64); ok {
			tokenUsage.CompletionTokens = int(completionTokens)
		}
		if totalTokens, ok := usage["total_tokens"].(float64); ok {
			tokenUsage.TotalTokens = int(totalTokens)
		}
	}

	// 打印 token 使用情况
	logger.Debug("StepFun API Token Usage: prompt=%d, completion=%d, total=%d",
		tokenUsage.PromptTokens, tokenUsage.CompletionTokens, tokenUsage.TotalTokens)

	return content, tokenUsage, nil
}
