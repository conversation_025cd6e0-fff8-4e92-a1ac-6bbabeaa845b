package impl

import (
	"bytes"
	"encoding/json"
	"errors"
	"log" // 添加日志包
	"net/http"
)

const (
	API_URL = "https://api.openai.com/v1/chat/completions"
)

func OpenAI(systemPrompt string, userPrompt string) (string, error) {
	requestBody, err := json.Marshal(map[string]interface{}{
		"model": "gpt-3.5-turbo",
		"messages": []map[string]string{
			{"role": "system", "content": systemPrompt},
			{"role": "user", "content": userPrompt},
		},
	})
	if err != nil {
		return "", err
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", API_URL, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+"")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}

	// 提取生成的文本
	choices, ok := result["choices"].([]interface{})
	if !ok || len(choices) == 0 {
		return "", errors.New("unexpected response format: no choices found")
	}

	message, ok := choices[0].(map[string]interface{})["message"].(map[string]interface{})
	if !ok {
		return "", errors.New("unexpected response format: no message found")
	}

	content, ok := message["content"].(string)
	if !ok {
		return "", errors.New("unexpected response format: no content found")
	}

	// 打印耗费 token 信息和响应内容
	log.Printf("\n============ Usage ==============\n%+v\n============ Response ==============\n%s\n===========================\n\n\n", result["usage"], content)

	return content, nil
}
