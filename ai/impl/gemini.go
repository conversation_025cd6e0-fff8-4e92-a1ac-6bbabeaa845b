package impl

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"fastserver.com/fastserver/ai"
	"fastserver.com/fastserver/config"
)

const GeminiProviderName = "gemini"

// GeminiProvider Gemini 实现
type GeminiProvider struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	models     []string
}

// GeminiContent Gemini API 内容格式
type GeminiContent struct {
	Parts []struct {
		Text string `json:"text"`
	} `json:"parts"`
	Role string `json:"role,omitempty"`
}

// GeminiRequest Gemini API 请求格式
type GeminiRequest struct {
	Contents         []GeminiContent `json:"contents"`
	GenerationConfig struct {
		Temperature     float64 `json:"temperature,omitempty"`
		MaxOutputTokens int     `json:"maxOutputTokens,omitempty"`
	} `json:"generationConfig,omitempty"`
}

// GeminiResponse Gemini API 响应格式
type GeminiResponse struct {
	Candidates []struct {
		Content GeminiContent `json:"content"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int `json:"promptTokenCount"`
		CandidatesTokenCount int `json:"candidatesTokenCount"`
		TotalTokenCount      int `json:"totalTokenCount"`
	} `json:"usageMetadata"`
}

// NewGeminiProvider 创建新的 Gemini 提供商
func NewGeminiProvider(apiKey string) *GeminiProvider {
	return &GeminiProvider{
		apiKey:  apiKey,
		baseURL: "https://generativelanguage.googleapis.com/v1beta",
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		models: []string{
			"gemini-2.5-flash-lite-preview-06-17",
			"gemini-1.5-pro-latest",
			"gemini-1.5-flash-latest",
			"gemini-pro",
		},
	}
}

// NewGemini 创建新的 Gemini 提供商（兼容配置结构）
func NewGemini(conf *config.AIConfig) (ai.LLMProvider, error) {
	if conf.Token == "" {
		return nil, fmt.Errorf("gemini token is empty")
	}

	provider := NewGeminiProvider(conf.Token)

	// 如果配置中指定了模型，将其设为默认模型
	if conf.Model != "" {
		// 检查模型是否在支持列表中
		modelSupported := false
		for _, model := range provider.models {
			if model == conf.Model {
				modelSupported = true
				break
			}
		}
		if !modelSupported {
			// 如果不在列表中，添加到列表开头作为默认模型
			provider.models = append([]string{conf.Model}, provider.models...)
		}
	}

	return provider, nil
}

// GetName 实现 LLMProvider 接口
func (p *GeminiProvider) GetName() string {
	return "gemini"
}

// Completion 实现 LLMProvider 接口
func (p *GeminiProvider) Completion(ctx context.Context, req ai.CompletionRequest) (*ai.CompletionResponse, error) {
	// 设置默认模型
	model := req.Model
	if model == "" {
		model = p.GetDefaultModel()
	}

	// 转换消息格式
	var contents []GeminiContent
	var systemInstruction string

	for _, msg := range req.Messages {
		if msg.Role == "system" {
			// Gemini 单独处理系统消息
			systemInstruction = msg.Content
			continue
		}

		// 转换角色名称
		role := msg.Role
		if role == "assistant" {
			role = "model"
		}

		content := GeminiContent{
			Parts: []struct {
				Text string `json:"text"`
			}{{Text: msg.Content}},
			Role: role,
		}
		contents = append(contents, content)
	}

	// 如果有系统指令，将其加入第一个用户消息
	if systemInstruction != "" && len(contents) > 0 {
		contents[0].Parts[0].Text = systemInstruction + "\n\n" + contents[0].Parts[0].Text
	}

	// 构建 Gemini 请求
	geminiReq := GeminiRequest{
		Contents: contents,
	}

	if req.Temperature > 0 {
		geminiReq.GenerationConfig.Temperature = req.Temperature
	}
	if req.MaxTokens > 0 {
		geminiReq.GenerationConfig.MaxOutputTokens = req.MaxTokens
	}

	// 序列化请求
	reqBody, err := json.Marshal(geminiReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 构建 URL
	url := fmt.Sprintf("%s/models/%s:generateContent?key=%s", p.baseURL, model, p.apiKey)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var geminiResp GeminiResponse
	if err := json.Unmarshal(respBody, &geminiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查响应是否有效
	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("no candidates in response")
	}

	return &ai.CompletionResponse{
		Content: geminiResp.Candidates[0].Content.Parts[0].Text,
		TokenUsage: ai.TokenUsage{
			PromptTokens:     geminiResp.UsageMetadata.PromptTokenCount,
			CompletionTokens: geminiResp.UsageMetadata.CandidatesTokenCount,
			TotalTokens:      geminiResp.UsageMetadata.TotalTokenCount,
		},
	}, nil
}

// SimpleCompletion 实现 LLMProvider 接口
func (p *GeminiProvider) SimpleCompletion(ctx context.Context, system, user string) (string, ai.TokenUsage, error) {
	messages := []ai.Message{
		{Role: "system", Content: system},
		{Role: "user", Content: user},
	}

	req := ai.CompletionRequest{
		Messages: messages,
	}

	resp, err := p.Completion(ctx, req)
	if err != nil {
		return "", ai.TokenUsage{}, err
	}

	return resp.Content, resp.TokenUsage, nil
}

// IsAvailable 实现 LLMProvider 接口
func (p *GeminiProvider) IsAvailable(ctx context.Context) bool {
	// 创建一个简单的测试请求
	url := fmt.Sprintf("%s/models?key=%s", p.baseURL, p.apiKey)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false
	}

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// GetModels 实现 LLMProvider 接口
func (p *GeminiProvider) GetModels() []string {
	return p.models
}

// GetDefaultModel 实现 LLMProvider 接口
func (p *GeminiProvider) GetDefaultModel() string {
	if len(p.models) > 0 {
		return p.models[0]
	}
	return "gemini-2.5-flash-lite-preview-06-17"
}

// STT 相关的结构体定义

// GeminiSTTPart STT 请求中的部分内容
type GeminiSTTPart struct {
	Text       string               `json:"text,omitempty"`
	InlineData *GeminiSTTInlineData `json:"inline_data,omitempty"`
}

// GeminiSTTInlineData 内联数据格式
type GeminiSTTInlineData struct {
	MimeType string `json:"mime_type"`
	Data     string `json:"data"` // Base64 编码的音频数据
}

// GeminiSTTContent STT 请求内容
type GeminiSTTContent struct {
	Parts []GeminiSTTPart `json:"parts"`
}

// GeminiSTTRequest STT 请求格式
type GeminiSTTRequest struct {
	Contents []GeminiSTTContent `json:"contents"`
}

// GeminiSTTResponse STT 响应格式
type GeminiSTTResponse struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text string `json:"text"`
			} `json:"parts"`
		} `json:"content"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int `json:"promptTokenCount"`
		CandidatesTokenCount int `json:"candidatesTokenCount"`
		TotalTokenCount      int `json:"totalTokenCount"`
	} `json:"usageMetadata"`
	Error *struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Status  string `json:"status"`
	} `json:"error,omitempty"`
}

// TranscribeAudio 实现 STTProvider 接口
func (p *GeminiProvider) TranscribeAudio(ctx context.Context, req ai.STTRequest) (*ai.STTResponse, error) {
	// 设置默认模型
	model := req.Model
	if model == "" {
		model = p.GetDefaultModel()
	}

	// 将音频数据编码为 base64
	audioBase64 := base64.StdEncoding.EncodeToString(req.AudioData)

	// 构建请求内容
	var parts []GeminiSTTPart

	// 如果有自定义提示词，添加到请求中
	if req.Prompt != "" {
		parts = append(parts, GeminiSTTPart{
			Text: req.Prompt,
		})
	} else {
		// 使用默认的转录提示词
		parts = append(parts, GeminiSTTPart{
			Text: "请将音频内容转录为文本。",
		})
	}

	// 添加音频数据
	parts = append(parts, GeminiSTTPart{
		InlineData: &GeminiSTTInlineData{
			MimeType: req.MimeType,
			Data:     audioBase64,
		},
	})

	// 构建 Gemini STT 请求
	geminiReq := GeminiSTTRequest{
		Contents: []GeminiSTTContent{
			{
				Parts: parts,
			},
		},
	}

	// 序列化请求
	reqBody, err := json.Marshal(geminiReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal STT request: %w", err)
	}

	// 构建 URL
	url := fmt.Sprintf("%s/models/%s:generateContent?key=%s", p.baseURL, model, p.apiKey)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create STT request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send STT request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read STT response: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("STT API request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var geminiResp GeminiSTTResponse
	if err := json.Unmarshal(respBody, &geminiResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal STT response: %w", err)
	}

	// 检查是否有错误
	if geminiResp.Error != nil {
		return nil, fmt.Errorf("STT API error: %s", geminiResp.Error.Message)
	}

	// 检查响应是否有效
	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("no transcription result in response")
	}

	return &ai.STTResponse{
		Text: geminiResp.Candidates[0].Content.Parts[0].Text,
		TokenUsage: ai.TokenUsage{
			PromptTokens:     geminiResp.UsageMetadata.PromptTokenCount,
			CompletionTokens: geminiResp.UsageMetadata.CandidatesTokenCount,
			TotalTokens:      geminiResp.UsageMetadata.TotalTokenCount,
		},
	}, nil
}

// GetSupportedFormats 实现 STTProvider 接口
func (p *GeminiProvider) GetSupportedFormats() []string {
	return []string{
		"audio/mp3",
		"audio/wav",
		"audio/m4a",
		"audio/ogg",
		"audio/flac",
		"audio/pcm",
	}
}
