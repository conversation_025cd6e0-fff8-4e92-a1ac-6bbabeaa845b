package impl

import (
	"context"
	"errors"

	"fastserver.com/fastserver/ai"
	"fastserver.com/fastserver/config"
	"github.com/sashabaranov/go-openai"
)

const SiliconflowProvider = "siliconflow"

// Siliconflow implements the ai.LLMProvider interface.
type Siliconflow struct {
	client          *openai.Client
	defaultModel    string
	supportedModels []string
}

// NewSiliconflow creates a new Siliconflow AI provider.
func NewSiliconflow(conf *config.AIConfig) (ai.LLMProvider, error) {
	if conf.Token == "" {
		return nil, errors.New("siliconflow token is empty")
	}
	clientConfig := openai.DefaultConfig(conf.Token)
	clientConfig.BaseURL = conf.Endpoint

	sf := &Siliconflow{
		client:          openai.NewClientWithConfig(clientConfig),
		defaultModel:    conf.Model,
		supportedModels: conf.Models,
	}
	return sf, nil
}

// GetName returns the name of the provider.
func (sf *Siliconflow) GetName() string {
	return SiliconflowProvider
}

// Completion performs a chat completion.
func (sf *Siliconflow) Completion(ctx context.Context, req ai.CompletionRequest) (*ai.CompletionResponse, error) {
	openaiMessages := make([]openai.ChatCompletionMessage, len(req.Messages))
	for i, msg := range req.Messages {
		openaiMessages[i] = openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	model := req.Model
	if model == "" {
		model = sf.GetDefaultModel()
	}

	openaiReq := openai.ChatCompletionRequest{
		Model:       model,
		Messages:    openaiMessages,
		MaxTokens:   req.MaxTokens,
		Temperature: float32(req.Temperature),
	}

	resp, err := sf.client.CreateChatCompletion(ctx, openaiReq)
	if err != nil {
		return nil, err
	}

	return &ai.CompletionResponse{
		Content: resp.Choices[0].Message.Content,
		TokenUsage: ai.TokenUsage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		},
	}, nil
}

// SimpleCompletion provides a simplified interface for single-turn chat.
func (sf *Siliconflow) SimpleCompletion(ctx context.Context, system, user string) (string, ai.TokenUsage, error) {
	req := ai.CompletionRequest{
		Messages: []ai.Message{
			{Role: "system", Content: system},
			{Role: "user", Content: user},
		},
	}
	resp, err := sf.Completion(ctx, req)
	if err != nil {
		return "", ai.TokenUsage{}, err
	}
	return resp.Content, resp.TokenUsage, nil
}

// IsAvailable checks if the service is available.
func (sf *Siliconflow) IsAvailable(ctx context.Context) bool {
	// A simple check might be to list models, but for now, we'll assume it's available if the client was created.
	// A more robust implementation could ping an endpoint.
	_, err := sf.client.ListModels(ctx)
	return err == nil
}

// GetModels returns the list of supported models.
func (sf *Siliconflow) GetModels() []string {
	return sf.supportedModels
}

// GetDefaultModel returns the default model.
func (sf *Siliconflow) GetDefaultModel() string {
	return sf.defaultModel
}
