# Fast Server

一个基于 Connect RPC 的高性能 AI 服务器，提供语音转文字、任务分析、应用商店等服务。支持多种 AI 提供商，包括 OpenAI、Gemini、SiliconFlow 等。

## 🚀 快速开始

### 环境要求

- Go 1.21+
- Protocol Buffers

### 安装和运行

```bash
# 克隆项目
git clone <repository-url>
cd fast-server

# 安装依赖
go mod download

# 配置环境（参考配置说明）
cp config/config.yaml config/config.local.yaml
# 编辑 config.local.yaml 填入你的 API Keys
# 📝 配置文件参考: config/config.yaml

# 运行服务
make run
# 或者
go run .
```

服务默认运行在 `http://localhost:8080`

### 健康检查

```bash
curl http://localhost:8080/health
# 返回: OK
```

## 🌟 主要功能

### 1. 任务分析
- **文本任务解析**：AI 驱动的智能任务解析，支持结构化输出
- **语音任务解析**：支持多种音频格式（MP3、WAV、M4A、OGG、FLAC、PCM）
- **智能应用匹配**：根据用户已安装应用优化识别结果  
- **时间解析**：支持自然语言时间描述和重复任务识别

### 3. 应用商店
- 预设插件管理
- 插件搜索和加载
- 深度链接提交

### 4. 多 AI 提供商支持
- **Gemini**: Google 的先进语言模型
- **SiliconFlow**: 高性能推理服务
- **OpenAI**: GPT 系列模型（通过扩展）
- **StepAI**: 专业 AI 服务

## ⚙️ 配置说明

### 配置文件结构

> 📁 **配置文件**: [`config/config.yaml`](config/config.yaml)

```yaml
server:
  port: 8080
  env: "development"  # development, staging, production

auth:
  supabase:
    url: "your-supabase-url"
    anon_key: "your-anon-key"
    database_url: "your-database-url"
  exclude_paths:  # 无需认证的路径
    - "/health"
    - "/proto.task.v1.TaskService/ParseVoiceTask"

log:
  level: "info"  # debug, info, warn, error

ai:
  provider: "gemini"  # gemini, siliconflow
  model: "gemini-2.5-flash-lite-preview-06-17"
  token: "your-api-key"
  format: "json"

stt:
  provider: "google-cloud-stt"  # gemini, google-cloud-stt
  google_cloud_stt:
    api_key: "your-google-cloud-api-key"
    project_id: "your-google-cloud-project-id"  # V2 API必需
    options:
      use_enhanced: true
      automatic_punctuation: true
      model: "default"
```

### 环境变量支持

```bash
# AI 配置
export AI_PROVIDER=gemini
export AI_TOKEN=your-api-key

# STT 配置
export STT_PROVIDER=google-cloud-stt
export GOOGLE_CLOUD_STT_API_KEY=your-google-cloud-api-key
export GOOGLE_CLOUD_PROJECT_ID=your-google-cloud-project-id

# Supabase 配置
export SUPABASE_URL=your-supabase-url
export SUPABASE_ANON_KEY=your-anon-key

# 服务器配置
export SERVER_PORT=8080
```

## 📖 API 使用示例

### 文本任务分析

```bash
# 分析文本任务
curl -X POST http://localhost:8080/proto.task.v1.TaskService/Analyze \
  -H "Content-Type: application/json" \
  -H "bluespace-timestamp: $(date +%s)" \
  -H "bluespace-timezone: Asia/Shanghai" \
  -d '{"input": "明天下午3点提醒我开会"}'
```

### 语音任务解析

```bash
# 解析语音中的任务信息（业务向接口）
curl -X POST http://localhost:8080/proto.task.v1.TaskService/ParseVoiceTask \
  -H "Content-Type: application/json" \
  -H "bluespace-timestamp: $(date +%s)" \
  -H "bluespace-timezone: Asia/Shanghai" \
  -d '{
    "audio_data": "base64_encoded_audio_data",
    "audio_format": 1,
    "language_code": "zh-CN",
    "installed_apps": ["微信", "支付宝", "淘宝", "美团"]
  }'
```

### 应用商店

```bash
# 获取预设插件
curl http://localhost:8080/proto.store.v1.StoreService/GetPresetPlugins

# 搜索插件
curl -X POST http://localhost:8080/proto.store.v1.StoreService/LoadMorePlugins \
  -H "Content-Type: application/json" \
  -d '{
    "query": "音乐",
    "limit": 10
  }'
```

## 🔧 开发指南

### 项目结构

```
fast-server/
├── ai/                 # AI 提供商抽象层
│   ├── interface.go    # 接口定义
│   └── impl/          # 具体实现
├── biz/               # 业务逻辑层
│   ├── task/          # 任务分析服务（包含语音任务解析）
│   ├── store/         # 应用商店服务
│   ├── submission/    # 提交服务
│   ├── update/        # 更新服务
│   └── middleware/    # 中间件
├── config/            # 配置文件
├── proto/             # Protobuf 定义
├── gen/               # 生成的代码
└── utils/             # 工具函数
```

### 核心文件快速访问

- 🏗️ **主程序**: [`main.go`](main.go)
- ⚙️ **配置文件**: [`config/config.yaml`](config/config.yaml)
- 🧠 **AI接口定义**: [`ai/interface.go`](ai/interface.go)
- 📋 **任务服务**: [`biz/task/task.go`](biz/task/task.go)
- 📡 **API定义**: [`proto/`](proto/) (Protobuf文件)
- 📦 **依赖管理**: [`go.mod`](go.mod)
- 🔨 **构建脚本**: [`Makefile`](Makefile)

### 构建和部署

> 📋 **构建配置**: [`Makefile`](Makefile)

```bash
# 本地构建
make build

# 构建 Linux 版本
make build_linux_amd64

# 部署到云服务器（需配置）
make deploy

# 构建并打标签
make deploy_and_tag

# 清理构建文件
make clean
```

### 添加新的 AI 提供商

1. 在 [`ai/impl/`](ai/impl/) 目录下创建新的提供商实现
2. 实现 [`ai/interface.go`](ai/interface.go) 中的 `LLMProvider` 和/或 `STTProvider` 接口
3. 在 [`main.go`](main.go) 的 `NewAIProvider` 函数中添加新提供商
4. 更新 [`config/config.yaml`](config/config.yaml) 支持新提供商

**参考实现示例**:
- 🤖 **Gemini**: [`ai/impl/gemini.go`](ai/impl/gemini.go)
- ⚡ **SiliconFlow**: [`ai/impl/siliconflow.go`](ai/impl/siliconflow.go)
- 🔥 **OpenAI**: [`ai/impl/openai.go`](ai/impl/openai.go)

## 🛡️ 认证

项目使用 Supabase 进行认证。某些路径可以配置为无需认证（如健康检查、语音任务解析等）。

认证头格式：
```
Authorization: Bearer your-jwt-token
```

## 🎙️ Google Cloud Speech-to-Text V2 API 配置

### 重要说明

使用 Google Cloud Speech-to-Text V2 API 需要额外的配置：

1. **项目ID必需**：V2 API 不支持占位符 `projects/_`，必须使用真实的项目ID
2. **API权限**：确保在Google Cloud Console中启用了Speech-to-Text V2 API
3. **API密钥权限**：API密钥需要有 `Cloud Speech Client` 权限

### 配置步骤

1. 在 [Google Cloud Console](https://console.cloud.google.com/) 创建项目
2. 启用 Speech-to-Text API
3. 创建API密钥并设置权限
4. 在 `config/config.yaml` 中配置：
   ```yaml
   stt:
     provider: "google-cloud-stt"
     google_cloud_stt:
       api_key: "your-actual-api-key"
       project_id: "your-actual-project-id"  # 不能是占位符
   ```

### 验证配置

```bash
# 运行配置验证测试
./test_v2_project_id.sh
```

### 详细文档

- 📖 [V2 API 设置指南](doc/google-cloud-stt-v2-setup.md)
- 🔧 [STT 升级指南](doc/stt-upgrade-guide.md)

## 📚 相关文档

- [Connect RPC 官方文档](https://connectrpc.com/)
- [Protobuf 指南](https://developers.google.com/protocol-buffers)
- [Supabase 文档](https://supabase.com/docs)

## 🤝 贡献

欢迎提交 Pull Request 和 Issue！

## �� 许可证

[添加你的许可证信息] 