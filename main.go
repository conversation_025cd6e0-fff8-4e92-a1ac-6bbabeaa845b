package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"

	"fastserver.com/fastserver/ai"
	"fastserver.com/fastserver/ai/impl"
	"fastserver.com/fastserver/biz/middleware"
	"fastserver.com/fastserver/biz/store"
	"fastserver.com/fastserver/biz/submission"
	"fastserver.com/fastserver/biz/task"
	"fastserver.com/fastserver/biz/update"
	"fastserver.com/fastserver/config"
	storev1connect "fastserver.com/fastserver/gen/proto/store/v1/v1connect"
	submissionv1connect "fastserver.com/fastserver/gen/proto/submission/v1/submissionv1connect"
	taskv1connect "fastserver.com/fastserver/gen/proto/task/v1/v1connect"
	updatev1connect "fastserver.com/fastserver/gen/proto/update/v1/v1connect"
	"fastserver.com/fastserver/utils/logger"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
)

func main() {
	// Load config
	if err := config.Load(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		return
	}
	logger.Info("Config loaded successfully")

	// Initialize DB Client
	dbClient, err := store.NewDBClient()
	if err != nil {
		logger.Error("Failed to create DB client: %v", err)
		return
	}

	// Initialize AI Provider
	aiProvider, err := NewAIProvider(config.GetConfig())
	if err != nil {
		logger.Error("Failed to create AI provider: %v", err)
		return
	}

	mux := http.NewServeMux()
	authMiddleware := middleware.NewAuthMiddleware()

	// Helper to register services with auth middleware
	registerService := func(path string, handler http.Handler) {
		mux.Handle(path, authMiddleware.WrapHandler(handler))
	}

	// 创建STT提供商（支持专门的Google Cloud Speech-to-Text或使用AI提供商）
	sttProvider, err := NewSTTProvider(config.GetConfig(), aiProvider)
	if err != nil {
		logger.Error("Failed to create STT provider: %v", err)
		return
	}

	// --- Task Service (包含语音任务解析) ---
	taskHandler := task.NewTaskAnalyzer(aiProvider, sttProvider)
	taskPath, taskConnectHandler := taskv1connect.NewTaskServiceHandler(taskHandler)
	registerService(taskPath, taskConnectHandler)

	// --- Store Service ---
	storeHandler, err := store.NewStoreHandler()
	if err != nil {
		logger.Error("Failed to create store handler: %v", err)
		return
	}
	storePath, storeConnectHandler := storev1connect.NewStoreServiceHandler(storeHandler)
	registerService(storePath, storeConnectHandler)

	// --- Update Service ---
	updateHandler, err := update.NewUpdateHandler()
	if err != nil {
		logger.Error("Failed to create update handler: %v", err)
		return
	}
	updatePath, updateConnectHandler := updatev1connect.NewUpdateServiceHandler(updateHandler)
	registerService(updatePath, updateConnectHandler)

	// --- Submission Service ---
	submissionHandler := submission.NewSubmissionService(dbClient)
	submissionPath, submissionConnectHandler := submissionv1connect.NewSubmissionServiceHandler(submissionHandler)
	registerService(submissionPath, submissionConnectHandler)

	// --- Health and Config Endpoints ---
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("OK"))
	})
	mux.HandleFunc("/config", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		_ = json.NewEncoder(w).Encode(config.GetConfig())
	})

	// Start server
	port := config.GetConfig().Server.Port
	logger.Info("Starting server on port %d", port)

	if err := http.ListenAndServe(
		fmt.Sprintf(":%d", port),
		h2c.NewHandler(mux, &http2.Server{}),
	); err != nil {
		logger.Error("failed to start server: %v", err)
		os.Exit(1)
	}
}

func NewAIProvider(conf *config.Config) (ai.LLMProvider, error) {
	var p ai.LLMProvider
	var err error

	switch conf.AI.Provider {
	case impl.SiliconflowProvider:
		p, err = impl.NewSiliconflow(conf.AI)
	case impl.GeminiProviderName:
		p, err = impl.NewGemini(conf.AI)
	// Other providers can be added here
	default:
		return nil, fmt.Errorf("unknown AI provider: %s", conf.AI.Provider)
	}
	if err != nil {
		return nil, err
	}
	return p, nil
}

// NewSTTProvider 创建STT提供商
func NewSTTProvider(conf *config.Config, fallbackAI ai.LLMProvider) (ai.STTProvider, error) {
	// 如果没有配置STT，使用AI提供商作为STT（向后兼容）
	if conf.STT == nil {
		logger.Info("未配置STT，使用AI提供商作为STT")
		if sttProvider, ok := fallbackAI.(ai.STTProvider); ok {
			return sttProvider, nil
		}
		return nil, fmt.Errorf("AI provider does not support STT functionality")
	}

	switch conf.STT.Provider {
	case "google-cloud-stt":
		logger.Info("使用Google Cloud Speech-to-Text作为STT提供商")
		if conf.STT.GoogleCloudSTT == nil || conf.STT.GoogleCloudSTT.APIKey == "" {
			return nil, fmt.Errorf("Google Cloud Speech-to-Text API key is required")
		}
		if conf.STT.GoogleCloudSTT.ProjectID == "" {
			return nil, fmt.Errorf("Google Cloud Speech-to-Text project ID is required")
		}

		// 转换配置选项
		var options *impl.GoogleCloudSTTOptions
		if conf.STT.GoogleCloudSTT.Options != nil {
			options = &impl.GoogleCloudSTTOptions{
				UseEnhanced:          conf.STT.GoogleCloudSTT.Options.UseEnhanced,
				AutomaticPunctuation: conf.STT.GoogleCloudSTT.Options.AutomaticPunctuation,
				Model:                conf.STT.GoogleCloudSTT.Options.Model,
				ProfanityFilter:      conf.STT.GoogleCloudSTT.Options.ProfanityFilter,
				EnableWordConfidence: conf.STT.GoogleCloudSTT.Options.EnableWordConfidence,
			}
		}

		return impl.NewGoogleCloudSTTProvider(conf.STT.GoogleCloudSTT.APIKey, conf.STT.GoogleCloudSTT.ProjectID, options), nil

	case "gemini":
		logger.Info("使用Gemini作为STT提供商")
		// 如果配置了Gemini STT，使用配置的token，否则使用AI提供商
		if conf.STT.Gemini != nil && conf.STT.Gemini.Token != "" {
			geminiProvider, err := impl.NewGemini(&config.AIConfig{
				Provider: "gemini",
				Model:    conf.STT.Gemini.Model,
				Token:    conf.STT.Gemini.Token,
			})
			if err != nil {
				return nil, fmt.Errorf("failed to create Gemini STT provider: %w", err)
			}
			// Gemini同时实现了LLMProvider和STTProvider接口
			if sttProvider, ok := geminiProvider.(ai.STTProvider); ok {
				return sttProvider, nil
			}
			return nil, fmt.Errorf("Gemini provider does not support STT functionality")
		}
		// 使用现有的AI提供商
		if sttProvider, ok := fallbackAI.(ai.STTProvider); ok {
			return sttProvider, nil
		}
		return nil, fmt.Errorf("AI provider does not support STT functionality")

	default:
		return nil, fmt.Errorf("unknown STT provider: %s", conf.STT.Provider)
	}
}
