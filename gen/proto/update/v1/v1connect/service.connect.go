// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proto/update/v1/service.proto

package v1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "fastserver.com/fastserver/gen/proto/update/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// UpdateServiceName is the fully-qualified name of the UpdateService service.
	UpdateServiceName = "proto.update.v1.UpdateService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// UpdateServiceCheckUpdateProcedure is the fully-qualified name of the UpdateService's CheckUpdate
	// RPC.
	UpdateServiceCheckUpdateProcedure = "/proto.update.v1.UpdateService/CheckUpdate"
)

// UpdateServiceClient is a client for the proto.update.v1.UpdateService service.
type UpdateServiceClient interface {
	// CheckUpdate 检查是否有新版本可用
	CheckUpdate(context.Context, *connect.Request[v1.CheckUpdateRequest]) (*connect.Response[v1.CheckUpdateResponse], error)
}

// NewUpdateServiceClient constructs a client for the proto.update.v1.UpdateService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewUpdateServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) UpdateServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	updateServiceMethods := v1.File_proto_update_v1_service_proto.Services().ByName("UpdateService").Methods()
	return &updateServiceClient{
		checkUpdate: connect.NewClient[v1.CheckUpdateRequest, v1.CheckUpdateResponse](
			httpClient,
			baseURL+UpdateServiceCheckUpdateProcedure,
			connect.WithSchema(updateServiceMethods.ByName("CheckUpdate")),
			connect.WithClientOptions(opts...),
		),
	}
}

// updateServiceClient implements UpdateServiceClient.
type updateServiceClient struct {
	checkUpdate *connect.Client[v1.CheckUpdateRequest, v1.CheckUpdateResponse]
}

// CheckUpdate calls proto.update.v1.UpdateService.CheckUpdate.
func (c *updateServiceClient) CheckUpdate(ctx context.Context, req *connect.Request[v1.CheckUpdateRequest]) (*connect.Response[v1.CheckUpdateResponse], error) {
	return c.checkUpdate.CallUnary(ctx, req)
}

// UpdateServiceHandler is an implementation of the proto.update.v1.UpdateService service.
type UpdateServiceHandler interface {
	// CheckUpdate 检查是否有新版本可用
	CheckUpdate(context.Context, *connect.Request[v1.CheckUpdateRequest]) (*connect.Response[v1.CheckUpdateResponse], error)
}

// NewUpdateServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewUpdateServiceHandler(svc UpdateServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	updateServiceMethods := v1.File_proto_update_v1_service_proto.Services().ByName("UpdateService").Methods()
	updateServiceCheckUpdateHandler := connect.NewUnaryHandler(
		UpdateServiceCheckUpdateProcedure,
		svc.CheckUpdate,
		connect.WithSchema(updateServiceMethods.ByName("CheckUpdate")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proto.update.v1.UpdateService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case UpdateServiceCheckUpdateProcedure:
			updateServiceCheckUpdateHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedUpdateServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedUpdateServiceHandler struct{}

func (UnimplementedUpdateServiceHandler) CheckUpdate(context.Context, *connect.Request[v1.CheckUpdateRequest]) (*connect.Response[v1.CheckUpdateResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.update.v1.UpdateService.CheckUpdate is not implemented"))
}
