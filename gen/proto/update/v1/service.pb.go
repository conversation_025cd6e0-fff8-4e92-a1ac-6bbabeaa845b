// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/update/v1/service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CheckUpdateRequest 定义了检查更新的请求结构
type CheckUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpdateRequest) Reset() {
	*x = CheckUpdateRequest{}
	mi := &file_proto_update_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateRequest) ProtoMessage() {}

func (x *CheckUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_update_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateRequest.ProtoReflect.Descriptor instead.
func (*CheckUpdateRequest) Descriptor() ([]byte, []int) {
	return file_proto_update_v1_service_proto_rawDescGZIP(), []int{0}
}

// CheckUpdateResponse 定义了检查更新的响应结构
type CheckUpdateResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	HasUpdate      bool                   `protobuf:"varint,1,opt,name=has_update,json=hasUpdate,proto3" json:"has_update,omitempty"`               // 是否有更新可用
	ForceUpdate    bool                   `protobuf:"varint,2,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"`         // 是否强制更新
	CurrentVersion string                 `protobuf:"bytes,3,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"` // 当前版本
	LatestVersion  string                 `protobuf:"bytes,4,opt,name=latest_version,json=latestVersion,proto3" json:"latest_version,omitempty"`    // 最新版本
	ReleaseNote    string                 `protobuf:"bytes,5,opt,name=release_note,json=releaseNote,proto3" json:"release_note,omitempty"`          // 更新说明
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckUpdateResponse) Reset() {
	*x = CheckUpdateResponse{}
	mi := &file_proto_update_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateResponse) ProtoMessage() {}

func (x *CheckUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_update_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateResponse.ProtoReflect.Descriptor instead.
func (*CheckUpdateResponse) Descriptor() ([]byte, []int) {
	return file_proto_update_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *CheckUpdateResponse) GetHasUpdate() bool {
	if x != nil {
		return x.HasUpdate
	}
	return false
}

func (x *CheckUpdateResponse) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

func (x *CheckUpdateResponse) GetCurrentVersion() string {
	if x != nil {
		return x.CurrentVersion
	}
	return ""
}

func (x *CheckUpdateResponse) GetLatestVersion() string {
	if x != nil {
		return x.LatestVersion
	}
	return ""
}

func (x *CheckUpdateResponse) GetReleaseNote() string {
	if x != nil {
		return x.ReleaseNote
	}
	return ""
}

var File_proto_update_v1_service_proto protoreflect.FileDescriptor

const file_proto_update_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x1dproto/update/v1/service.proto\x12\x0fproto.update.v1\"\x14\n" +
	"\x12CheckUpdateRequest\"\xca\x01\n" +
	"\x13CheckUpdateResponse\x12\x1d\n" +
	"\n" +
	"has_update\x18\x01 \x01(\bR\thasUpdate\x12!\n" +
	"\fforce_update\x18\x02 \x01(\bR\vforceUpdate\x12'\n" +
	"\x0fcurrent_version\x18\x03 \x01(\tR\x0ecurrentVersion\x12%\n" +
	"\x0elatest_version\x18\x04 \x01(\tR\rlatestVersion\x12!\n" +
	"\frelease_note\x18\x05 \x01(\tR\vreleaseNote2k\n" +
	"\rUpdateService\x12Z\n" +
	"\vCheckUpdate\x12#.proto.update.v1.CheckUpdateRequest\x1a$.proto.update.v1.CheckUpdateResponse\"\x00B/Z-fastserver.com/fastserver/gen/proto/update/v1b\x06proto3"

var (
	file_proto_update_v1_service_proto_rawDescOnce sync.Once
	file_proto_update_v1_service_proto_rawDescData []byte
)

func file_proto_update_v1_service_proto_rawDescGZIP() []byte {
	file_proto_update_v1_service_proto_rawDescOnce.Do(func() {
		file_proto_update_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_update_v1_service_proto_rawDesc), len(file_proto_update_v1_service_proto_rawDesc)))
	})
	return file_proto_update_v1_service_proto_rawDescData
}

var file_proto_update_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_update_v1_service_proto_goTypes = []any{
	(*CheckUpdateRequest)(nil),  // 0: proto.update.v1.CheckUpdateRequest
	(*CheckUpdateResponse)(nil), // 1: proto.update.v1.CheckUpdateResponse
}
var file_proto_update_v1_service_proto_depIdxs = []int32{
	0, // 0: proto.update.v1.UpdateService.CheckUpdate:input_type -> proto.update.v1.CheckUpdateRequest
	1, // 1: proto.update.v1.UpdateService.CheckUpdate:output_type -> proto.update.v1.CheckUpdateResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_update_v1_service_proto_init() }
func file_proto_update_v1_service_proto_init() {
	if File_proto_update_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_update_v1_service_proto_rawDesc), len(file_proto_update_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_update_v1_service_proto_goTypes,
		DependencyIndexes: file_proto_update_v1_service_proto_depIdxs,
		MessageInfos:      file_proto_update_v1_service_proto_msgTypes,
	}.Build()
	File_proto_update_v1_service_proto = out.File
	file_proto_update_v1_service_proto_goTypes = nil
	file_proto_update_v1_service_proto_depIdxs = nil
}
