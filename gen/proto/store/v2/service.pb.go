// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/store/v2/service.proto

package v2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AppBrief 定义了应用的基本信息
type AppBrief struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                   // 应用唯一标识符
	AppName       string                 `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`             // 应用名称
	PackageName   string                 `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` // 包名
	Language      string                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`                          // 语言
	IconUrl       string                 `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`             // 图标URL
	Categories    []string               `protobuf:"bytes,6,rep,name=categories,proto3" json:"categories,omitempty"`                      // 类别列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppBrief) Reset() {
	*x = AppBrief{}
	mi := &file_proto_store_v2_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppBrief) ProtoMessage() {}

func (x *AppBrief) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppBrief.ProtoReflect.Descriptor instead.
func (*AppBrief) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{0}
}

func (x *AppBrief) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppBrief) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AppBrief) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *AppBrief) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AppBrief) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *AppBrief) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

// PluginBrief 定义了插件的基本信息
type PluginBrief struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PluginId          string                 `protobuf:"bytes,1,opt,name=plugin_id,json=pluginId,proto3" json:"plugin_id,omitempty"`                            // 插件唯一标识符
	PluginName        string                 `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`                      // 名称       // 深度链接URL
	PluginType        string                 `protobuf:"bytes,3,opt,name=plugin_type,json=pluginType,proto3" json:"plugin_type,omitempty"`                      // 类型 (deeplink 或 action_send)
	PresetGroups      []string               `protobuf:"bytes,4,rep,name=preset_groups,json=presetGroups,proto3" json:"preset_groups,omitempty"`                // 预设组列表
	PluginIconUrl     string                 `protobuf:"bytes,5,opt,name=plugin_icon_url,json=pluginIconUrl,proto3" json:"plugin_icon_url,omitempty"`           // 图标URL
	PluginDescription string                 `protobuf:"bytes,6,opt,name=plugin_description,json=pluginDescription,proto3" json:"plugin_description,omitempty"` // 描述
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PluginBrief) Reset() {
	*x = PluginBrief{}
	mi := &file_proto_store_v2_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginBrief) ProtoMessage() {}

func (x *PluginBrief) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginBrief.ProtoReflect.Descriptor instead.
func (*PluginBrief) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{1}
}

func (x *PluginBrief) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *PluginBrief) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginBrief) GetPluginType() string {
	if x != nil {
		return x.PluginType
	}
	return ""
}

func (x *PluginBrief) GetPresetGroups() []string {
	if x != nil {
		return x.PresetGroups
	}
	return nil
}

func (x *PluginBrief) GetPluginIconUrl() string {
	if x != nil {
		return x.PluginIconUrl
	}
	return ""
}

func (x *PluginBrief) GetPluginDescription() string {
	if x != nil {
		return x.PluginDescription
	}
	return ""
}

// Based on the Kotlin LocalPlugin sealed class
type Plugin struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PluginId       string                 `protobuf:"bytes,1,opt,name=plugin_id,json=pluginId,proto3" json:"plugin_id,omitempty"`
	PluginName     string                 `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	AppName        string                 `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AppPackageName string                 `protobuf:"bytes,4,opt,name=app_package_name,json=appPackageName,proto3" json:"app_package_name,omitempty"` // package name for applications
	Categories     []string               `protobuf:"bytes,5,rep,name=categories,proto3" json:"categories,omitempty"`
	IconUrl        *string                `protobuf:"bytes,6,opt,name=icon_url,json=iconUrl,proto3,oneof" json:"icon_url,omitempty"`
	// Types that are valid to be assigned to PluginType:
	//
	//	*Plugin_OpenAppPlugin
	//	*Plugin_DeeplinkPlugin
	//	*Plugin_ActionSendPlugin
	//	*Plugin_ProcessTextPlugin
	//	*Plugin_WebhookPlugin
	PluginType    isPlugin_PluginType `protobuf_oneof:"plugin_type"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Plugin) Reset() {
	*x = Plugin{}
	mi := &file_proto_store_v2_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Plugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plugin) ProtoMessage() {}

func (x *Plugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plugin.ProtoReflect.Descriptor instead.
func (*Plugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{2}
}

func (x *Plugin) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *Plugin) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *Plugin) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *Plugin) GetAppPackageName() string {
	if x != nil {
		return x.AppPackageName
	}
	return ""
}

func (x *Plugin) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *Plugin) GetIconUrl() string {
	if x != nil && x.IconUrl != nil {
		return *x.IconUrl
	}
	return ""
}

func (x *Plugin) GetPluginType() isPlugin_PluginType {
	if x != nil {
		return x.PluginType
	}
	return nil
}

func (x *Plugin) GetOpenAppPlugin() *OpenAppPlugin {
	if x != nil {
		if x, ok := x.PluginType.(*Plugin_OpenAppPlugin); ok {
			return x.OpenAppPlugin
		}
	}
	return nil
}

func (x *Plugin) GetDeeplinkPlugin() *DeeplinkPlugin {
	if x != nil {
		if x, ok := x.PluginType.(*Plugin_DeeplinkPlugin); ok {
			return x.DeeplinkPlugin
		}
	}
	return nil
}

func (x *Plugin) GetActionSendPlugin() *ActionSendPlugin {
	if x != nil {
		if x, ok := x.PluginType.(*Plugin_ActionSendPlugin); ok {
			return x.ActionSendPlugin
		}
	}
	return nil
}

func (x *Plugin) GetProcessTextPlugin() *ProcessTextPlugin {
	if x != nil {
		if x, ok := x.PluginType.(*Plugin_ProcessTextPlugin); ok {
			return x.ProcessTextPlugin
		}
	}
	return nil
}

func (x *Plugin) GetWebhookPlugin() *WebhookPlugin {
	if x != nil {
		if x, ok := x.PluginType.(*Plugin_WebhookPlugin); ok {
			return x.WebhookPlugin
		}
	}
	return nil
}

type isPlugin_PluginType interface {
	isPlugin_PluginType()
}

type Plugin_OpenAppPlugin struct {
	OpenAppPlugin *OpenAppPlugin `protobuf:"bytes,7,opt,name=open_app_plugin,json=openAppPlugin,proto3,oneof"`
}

type Plugin_DeeplinkPlugin struct {
	DeeplinkPlugin *DeeplinkPlugin `protobuf:"bytes,8,opt,name=deeplink_plugin,json=deeplinkPlugin,proto3,oneof"`
}

type Plugin_ActionSendPlugin struct {
	ActionSendPlugin *ActionSendPlugin `protobuf:"bytes,9,opt,name=action_send_plugin,json=actionSendPlugin,proto3,oneof"`
}

type Plugin_ProcessTextPlugin struct {
	ProcessTextPlugin *ProcessTextPlugin `protobuf:"bytes,10,opt,name=process_text_plugin,json=processTextPlugin,proto3,oneof"`
}

type Plugin_WebhookPlugin struct {
	WebhookPlugin *WebhookPlugin `protobuf:"bytes,11,opt,name=webhook_plugin,json=webhookPlugin,proto3,oneof"`
}

func (*Plugin_OpenAppPlugin) isPlugin_PluginType() {}

func (*Plugin_DeeplinkPlugin) isPlugin_PluginType() {}

func (*Plugin_ActionSendPlugin) isPlugin_PluginType() {}

func (*Plugin_ProcessTextPlugin) isPlugin_PluginType() {}

func (*Plugin_WebhookPlugin) isPlugin_PluginType() {}

type OpenAppPlugin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OpenAppPlugin) Reset() {
	*x = OpenAppPlugin{}
	mi := &file_proto_store_v2_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenAppPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenAppPlugin) ProtoMessage() {}

func (x *OpenAppPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenAppPlugin.ProtoReflect.Descriptor instead.
func (*OpenAppPlugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{3}
}

type DeeplinkPlugin struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	Deeplink                string                 `protobuf:"bytes,1,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Replacement             string                 `protobuf:"bytes,2,opt,name=replacement,proto3" json:"replacement,omitempty"`
	SpecifiedAppPackageName *string                `protobuf:"bytes,3,opt,name=specified_app_package_name,json=specifiedAppPackageName,proto3,oneof" json:"specified_app_package_name,omitempty"` // force open with a app (empty for system choosing)
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *DeeplinkPlugin) Reset() {
	*x = DeeplinkPlugin{}
	mi := &file_proto_store_v2_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeeplinkPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeeplinkPlugin) ProtoMessage() {}

func (x *DeeplinkPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeeplinkPlugin.ProtoReflect.Descriptor instead.
func (*DeeplinkPlugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeeplinkPlugin) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *DeeplinkPlugin) GetReplacement() string {
	if x != nil {
		return x.Replacement
	}
	return ""
}

func (x *DeeplinkPlugin) GetSpecifiedAppPackageName() string {
	if x != nil && x.SpecifiedAppPackageName != nil {
		return *x.SpecifiedAppPackageName
	}
	return ""
}

type ActionSendPlugin struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	SpecifiedComponent *string                `protobuf:"bytes,1,opt,name=specified_component,json=specifiedComponent,proto3,oneof" json:"specified_component,omitempty"` // when calling action_send, passing component if this field exist -- it's a activity class path
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ActionSendPlugin) Reset() {
	*x = ActionSendPlugin{}
	mi := &file_proto_store_v2_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionSendPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionSendPlugin) ProtoMessage() {}

func (x *ActionSendPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionSendPlugin.ProtoReflect.Descriptor instead.
func (*ActionSendPlugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{5}
}

func (x *ActionSendPlugin) GetSpecifiedComponent() string {
	if x != nil && x.SpecifiedComponent != nil {
		return *x.SpecifiedComponent
	}
	return ""
}

type ProcessTextPlugin struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	SpecifiedComponent *string                `protobuf:"bytes,1,opt,name=specified_component,json=specifiedComponent,proto3,oneof" json:"specified_component,omitempty"` // when calling action_process_text, passing component if this field exist -- it's a activity class path
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ProcessTextPlugin) Reset() {
	*x = ProcessTextPlugin{}
	mi := &file_proto_store_v2_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessTextPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessTextPlugin) ProtoMessage() {}

func (x *ProcessTextPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessTextPlugin.ProtoReflect.Descriptor instead.
func (*ProcessTextPlugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessTextPlugin) GetSpecifiedComponent() string {
	if x != nil && x.SpecifiedComponent != nil {
		return *x.SpecifiedComponent
	}
	return ""
}

type WebhookPlugin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WebhookConfig string                 `protobuf:"bytes,1,opt,name=webhook_config,json=webhookConfig,proto3" json:"webhook_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebhookPlugin) Reset() {
	*x = WebhookPlugin{}
	mi := &file_proto_store_v2_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookPlugin) ProtoMessage() {}

func (x *WebhookPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v2_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookPlugin.ProtoReflect.Descriptor instead.
func (*WebhookPlugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v2_service_proto_rawDescGZIP(), []int{7}
}

func (x *WebhookPlugin) GetWebhookConfig() string {
	if x != nil {
		return x.WebhookConfig
	}
	return ""
}

var File_proto_store_v2_service_proto protoreflect.FileDescriptor

const file_proto_store_v2_service_proto_rawDesc = "" +
	"\n" +
	"\x1cproto/store/v2/service.proto\x12\x0eproto.store.v2\"\xb6\x01\n" +
	"\bAppBrief\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12\x19\n" +
	"\bapp_name\x18\x02 \x01(\tR\aappName\x12!\n" +
	"\fpackage_name\x18\x03 \x01(\tR\vpackageName\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x12\x19\n" +
	"\bicon_url\x18\x05 \x01(\tR\aiconUrl\x12\x1e\n" +
	"\n" +
	"categories\x18\x06 \x03(\tR\n" +
	"categories\"\xe8\x01\n" +
	"\vPluginBrief\x12\x1b\n" +
	"\tplugin_id\x18\x01 \x01(\tR\bpluginId\x12\x1f\n" +
	"\vplugin_name\x18\x02 \x01(\tR\n" +
	"pluginName\x12\x1f\n" +
	"\vplugin_type\x18\x03 \x01(\tR\n" +
	"pluginType\x12#\n" +
	"\rpreset_groups\x18\x04 \x03(\tR\fpresetGroups\x12&\n" +
	"\x0fplugin_icon_url\x18\x05 \x01(\tR\rpluginIconUrl\x12-\n" +
	"\x12plugin_description\x18\x06 \x01(\tR\x11pluginDescription\"\xea\x04\n" +
	"\x06Plugin\x12\x1b\n" +
	"\tplugin_id\x18\x01 \x01(\tR\bpluginId\x12\x1f\n" +
	"\vplugin_name\x18\x02 \x01(\tR\n" +
	"pluginName\x12\x19\n" +
	"\bapp_name\x18\x03 \x01(\tR\aappName\x12(\n" +
	"\x10app_package_name\x18\x04 \x01(\tR\x0eappPackageName\x12\x1e\n" +
	"\n" +
	"categories\x18\x05 \x03(\tR\n" +
	"categories\x12\x1e\n" +
	"\bicon_url\x18\x06 \x01(\tH\x01R\aiconUrl\x88\x01\x01\x12G\n" +
	"\x0fopen_app_plugin\x18\a \x01(\v2\x1d.proto.store.v2.OpenAppPluginH\x00R\ropenAppPlugin\x12I\n" +
	"\x0fdeeplink_plugin\x18\b \x01(\v2\x1e.proto.store.v2.DeeplinkPluginH\x00R\x0edeeplinkPlugin\x12P\n" +
	"\x12action_send_plugin\x18\t \x01(\v2 .proto.store.v2.ActionSendPluginH\x00R\x10actionSendPlugin\x12S\n" +
	"\x13process_text_plugin\x18\n" +
	" \x01(\v2!.proto.store.v2.ProcessTextPluginH\x00R\x11processTextPlugin\x12F\n" +
	"\x0ewebhook_plugin\x18\v \x01(\v2\x1d.proto.store.v2.WebhookPluginH\x00R\rwebhookPluginB\r\n" +
	"\vplugin_typeB\v\n" +
	"\t_icon_url\"\x0f\n" +
	"\rOpenAppPlugin\"\xaf\x01\n" +
	"\x0eDeeplinkPlugin\x12\x1a\n" +
	"\bdeeplink\x18\x01 \x01(\tR\bdeeplink\x12 \n" +
	"\vreplacement\x18\x02 \x01(\tR\vreplacement\x12@\n" +
	"\x1aspecified_app_package_name\x18\x03 \x01(\tH\x00R\x17specifiedAppPackageName\x88\x01\x01B\x1d\n" +
	"\x1b_specified_app_package_name\"`\n" +
	"\x10ActionSendPlugin\x124\n" +
	"\x13specified_component\x18\x01 \x01(\tH\x00R\x12specifiedComponent\x88\x01\x01B\x16\n" +
	"\x14_specified_component\"a\n" +
	"\x11ProcessTextPlugin\x124\n" +
	"\x13specified_component\x18\x01 \x01(\tH\x00R\x12specifiedComponent\x88\x01\x01B\x16\n" +
	"\x14_specified_component\"6\n" +
	"\rWebhookPlugin\x12%\n" +
	"\x0ewebhook_config\x18\x01 \x01(\tR\rwebhookConfigB.Z,fastserver.com/fastserver/gen/proto/store/v2b\x06proto3"

var (
	file_proto_store_v2_service_proto_rawDescOnce sync.Once
	file_proto_store_v2_service_proto_rawDescData []byte
)

func file_proto_store_v2_service_proto_rawDescGZIP() []byte {
	file_proto_store_v2_service_proto_rawDescOnce.Do(func() {
		file_proto_store_v2_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_store_v2_service_proto_rawDesc), len(file_proto_store_v2_service_proto_rawDesc)))
	})
	return file_proto_store_v2_service_proto_rawDescData
}

var file_proto_store_v2_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proto_store_v2_service_proto_goTypes = []any{
	(*AppBrief)(nil),          // 0: proto.store.v2.AppBrief
	(*PluginBrief)(nil),       // 1: proto.store.v2.PluginBrief
	(*Plugin)(nil),            // 2: proto.store.v2.Plugin
	(*OpenAppPlugin)(nil),     // 3: proto.store.v2.OpenAppPlugin
	(*DeeplinkPlugin)(nil),    // 4: proto.store.v2.DeeplinkPlugin
	(*ActionSendPlugin)(nil),  // 5: proto.store.v2.ActionSendPlugin
	(*ProcessTextPlugin)(nil), // 6: proto.store.v2.ProcessTextPlugin
	(*WebhookPlugin)(nil),     // 7: proto.store.v2.WebhookPlugin
}
var file_proto_store_v2_service_proto_depIdxs = []int32{
	3, // 0: proto.store.v2.Plugin.open_app_plugin:type_name -> proto.store.v2.OpenAppPlugin
	4, // 1: proto.store.v2.Plugin.deeplink_plugin:type_name -> proto.store.v2.DeeplinkPlugin
	5, // 2: proto.store.v2.Plugin.action_send_plugin:type_name -> proto.store.v2.ActionSendPlugin
	6, // 3: proto.store.v2.Plugin.process_text_plugin:type_name -> proto.store.v2.ProcessTextPlugin
	7, // 4: proto.store.v2.Plugin.webhook_plugin:type_name -> proto.store.v2.WebhookPlugin
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_store_v2_service_proto_init() }
func file_proto_store_v2_service_proto_init() {
	if File_proto_store_v2_service_proto != nil {
		return
	}
	file_proto_store_v2_service_proto_msgTypes[2].OneofWrappers = []any{
		(*Plugin_OpenAppPlugin)(nil),
		(*Plugin_DeeplinkPlugin)(nil),
		(*Plugin_ActionSendPlugin)(nil),
		(*Plugin_ProcessTextPlugin)(nil),
		(*Plugin_WebhookPlugin)(nil),
	}
	file_proto_store_v2_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_proto_store_v2_service_proto_msgTypes[5].OneofWrappers = []any{}
	file_proto_store_v2_service_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_store_v2_service_proto_rawDesc), len(file_proto_store_v2_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_store_v2_service_proto_goTypes,
		DependencyIndexes: file_proto_store_v2_service_proto_depIdxs,
		MessageInfos:      file_proto_store_v2_service_proto_msgTypes,
	}.Build()
	File_proto_store_v2_service_proto = out.File
	file_proto_store_v2_service_proto_goTypes = nil
	file_proto_store_v2_service_proto_depIdxs = nil
}
