// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/store/v1/service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Plugin 定义了插件的基本信息
type Plugin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                      // 插件唯一标识符
	AppName       string                 `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`             // 应用名称
	PackageName   string                 `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` // 包名
	Language      string                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`                          // 语言
	IconUrl       string                 `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`             // 图标URL
	Categories    []string               `protobuf:"bytes,6,rep,name=categories,proto3" json:"categories,omitempty"`                      // 类别列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Plugin) Reset() {
	*x = Plugin{}
	mi := &file_proto_store_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Plugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plugin) ProtoMessage() {}

func (x *Plugin) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plugin.ProtoReflect.Descriptor instead.
func (*Plugin) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Plugin) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Plugin) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *Plugin) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Plugin) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Plugin) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *Plugin) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

// Deeplink 定义了深度链接的基本信息
type Deeplink struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                           // 深度链接唯一标识符
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                       // 名称
	Deeplink          string                 `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`                                               // 深度链接URL
	Type              string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                                                       // 类型 (deeplink 或 action_send)
	Verified          bool                   `protobuf:"varint,5,opt,name=verified,proto3" json:"verified,omitempty"`                                              // 是否已验证
	RequiresTextInput bool                   `protobuf:"varint,6,opt,name=requires_text_input,json=requiresTextInput,proto3" json:"requires_text_input,omitempty"` // 是否需要文本输入
	OpenWithApp       string                 `protobuf:"bytes,7,opt,name=open_with_app,json=openWithApp,proto3" json:"open_with_app,omitempty"`                    //  用于 deeplink 支持多 App 时，指定打开的应用
	PresetGroups      []string               `protobuf:"bytes,8,rep,name=preset_groups,json=presetGroups,proto3" json:"preset_groups,omitempty"`                   // 预设组列表
	OpenWithActivity  string                 `protobuf:"bytes,9,opt,name=open_with_activity,json=openWithActivity,proto3" json:"open_with_activity,omitempty"`     // 用于 action_send 某 App 多 Activity 时，指定打开的 Activity
	Config            string                 `protobuf:"bytes,10,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Deeplink) Reset() {
	*x = Deeplink{}
	mi := &file_proto_store_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Deeplink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deeplink) ProtoMessage() {}

func (x *Deeplink) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deeplink.ProtoReflect.Descriptor instead.
func (*Deeplink) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *Deeplink) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Deeplink) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Deeplink) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *Deeplink) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Deeplink) GetVerified() bool {
	if x != nil {
		return x.Verified
	}
	return false
}

func (x *Deeplink) GetRequiresTextInput() bool {
	if x != nil {
		return x.RequiresTextInput
	}
	return false
}

func (x *Deeplink) GetOpenWithApp() string {
	if x != nil {
		return x.OpenWithApp
	}
	return ""
}

func (x *Deeplink) GetPresetGroups() []string {
	if x != nil {
		return x.PresetGroups
	}
	return nil
}

func (x *Deeplink) GetOpenWithActivity() string {
	if x != nil {
		return x.OpenWithActivity
	}
	return ""
}

func (x *Deeplink) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

// PluginDetail 定义了包含深度链接的完整插件信息
type PluginDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plugin        *Plugin                `protobuf:"bytes,1,opt,name=plugin,proto3" json:"plugin,omitempty"`       // 插件基本信息
	Deeplinks     []*Deeplink            `protobuf:"bytes,2,rep,name=deeplinks,proto3" json:"deeplinks,omitempty"` // 插件的深度链接列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginDetail) Reset() {
	*x = PluginDetail{}
	mi := &file_proto_store_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginDetail) ProtoMessage() {}

func (x *PluginDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginDetail.ProtoReflect.Descriptor instead.
func (*PluginDetail) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *PluginDetail) GetPlugin() *Plugin {
	if x != nil {
		return x.Plugin
	}
	return nil
}

func (x *PluginDetail) GetDeeplinks() []*Deeplink {
	if x != nil {
		return x.Deeplinks
	}
	return nil
}

// GetPluginRequest 定义了获取单个插件详情的请求
type GetPluginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // 插件ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPluginRequest) Reset() {
	*x = GetPluginRequest{}
	mi := &file_proto_store_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPluginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPluginRequest) ProtoMessage() {}

func (x *GetPluginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPluginRequest.ProtoReflect.Descriptor instead.
func (*GetPluginRequest) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPluginRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// GetPluginResponse 定义了获取单个插件详情的响应
type GetPluginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plugin        *PluginDetail          `protobuf:"bytes,1,opt,name=plugin,proto3" json:"plugin,omitempty"` // 插件详情
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPluginResponse) Reset() {
	*x = GetPluginResponse{}
	mi := &file_proto_store_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPluginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPluginResponse) ProtoMessage() {}

func (x *GetPluginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPluginResponse.ProtoReflect.Descriptor instead.
func (*GetPluginResponse) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetPluginResponse) GetPlugin() *PluginDetail {
	if x != nil {
		return x.Plugin
	}
	return nil
}

// GetCategoriesRequest 定义了获取所有类别的请求
type GetCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCategoriesRequest) Reset() {
	*x = GetCategoriesRequest{}
	mi := &file_proto_store_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoriesRequest) ProtoMessage() {}

func (x *GetCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{5}
}

// GetCategoriesResponse 定义了获取所有类别的响应
type GetCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Categories    []string               `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"` // 类别列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCategoriesResponse) Reset() {
	*x = GetCategoriesResponse{}
	mi := &file_proto_store_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoriesResponse) ProtoMessage() {}

func (x *GetCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetCategoriesResponse) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

// GetPresetGroupsRequest 定义了获取所有预设组的请求
type GetPresetGroupsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresetGroupsRequest) Reset() {
	*x = GetPresetGroupsRequest{}
	mi := &file_proto_store_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresetGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetGroupsRequest) ProtoMessage() {}

func (x *GetPresetGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetGroupsRequest.ProtoReflect.Descriptor instead.
func (*GetPresetGroupsRequest) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{7}
}

// GetPresetGroupsResponse 定义了获取所有预设组的响应
type GetPresetGroupsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PresetGroups  []string               `protobuf:"bytes,1,rep,name=preset_groups,json=presetGroups,proto3" json:"preset_groups,omitempty"` // 预设组列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresetGroupsResponse) Reset() {
	*x = GetPresetGroupsResponse{}
	mi := &file_proto_store_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresetGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetGroupsResponse) ProtoMessage() {}

func (x *GetPresetGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetGroupsResponse.ProtoReflect.Descriptor instead.
func (*GetPresetGroupsResponse) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetPresetGroupsResponse) GetPresetGroups() []string {
	if x != nil {
		return x.PresetGroups
	}
	return nil
}

// GetPresetPluginsRequest 定义了获取所有预设插件的请求
type GetPresetPluginsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Language      string                 `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"` // 可选的语言筛选
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresetPluginsRequest) Reset() {
	*x = GetPresetPluginsRequest{}
	mi := &file_proto_store_v1_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresetPluginsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetPluginsRequest) ProtoMessage() {}

func (x *GetPresetPluginsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetPluginsRequest.ProtoReflect.Descriptor instead.
func (*GetPresetPluginsRequest) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetPresetPluginsRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

// GetPresetPluginsResponse 定义了获取所有预设插件的响应
type GetPresetPluginsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plugins       []*PluginDetail        `protobuf:"bytes,1,rep,name=plugins,proto3" json:"plugins,omitempty"` // 预设插件列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPresetPluginsResponse) Reset() {
	*x = GetPresetPluginsResponse{}
	mi := &file_proto_store_v1_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPresetPluginsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPresetPluginsResponse) ProtoMessage() {}

func (x *GetPresetPluginsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPresetPluginsResponse.ProtoReflect.Descriptor instead.
func (*GetPresetPluginsResponse) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetPresetPluginsResponse) GetPlugins() []*PluginDetail {
	if x != nil {
		return x.Plugins
	}
	return nil
}

// LoadMorePluginsRequest 定义了加载更多插件的请求参数
type LoadMorePluginsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Offset        int32                  `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`                             // 从第几条开始
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`                               // 获取多少条
	Category      string                 `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`                          // 按类别筛选
	Language      string                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`                          // 按语言筛选
	PresetGroup   string                 `protobuf:"bytes,5,opt,name=preset_group,json=presetGroup,proto3" json:"preset_group,omitempty"` // 按预设组筛选
	SearchTerm    string                 `protobuf:"bytes,6,opt,name=search_term,json=searchTerm,proto3" json:"search_term,omitempty"`    // 搜索关键词
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadMorePluginsRequest) Reset() {
	*x = LoadMorePluginsRequest{}
	mi := &file_proto_store_v1_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadMorePluginsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadMorePluginsRequest) ProtoMessage() {}

func (x *LoadMorePluginsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadMorePluginsRequest.ProtoReflect.Descriptor instead.
func (*LoadMorePluginsRequest) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *LoadMorePluginsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *LoadMorePluginsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *LoadMorePluginsRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *LoadMorePluginsRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *LoadMorePluginsRequest) GetPresetGroup() string {
	if x != nil {
		return x.PresetGroup
	}
	return ""
}

func (x *LoadMorePluginsRequest) GetSearchTerm() string {
	if x != nil {
		return x.SearchTerm
	}
	return ""
}

// LoadMorePluginsResponse 定义了加载更多插件的响应
type LoadMorePluginsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plugins       []*PluginDetail        `protobuf:"bytes,1,rep,name=plugins,proto3" json:"plugins,omitempty"`                          // 插件列表
	HasMore       bool                   `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`          // 是否还有更多数据
	NextOffset    int32                  `protobuf:"varint,3,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"` // 下一次请求的起始位置
	TotalCount    int32                  `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"` // 插件总数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadMorePluginsResponse) Reset() {
	*x = LoadMorePluginsResponse{}
	mi := &file_proto_store_v1_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadMorePluginsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadMorePluginsResponse) ProtoMessage() {}

func (x *LoadMorePluginsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_store_v1_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadMorePluginsResponse.ProtoReflect.Descriptor instead.
func (*LoadMorePluginsResponse) Descriptor() ([]byte, []int) {
	return file_proto_store_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *LoadMorePluginsResponse) GetPlugins() []*PluginDetail {
	if x != nil {
		return x.Plugins
	}
	return nil
}

func (x *LoadMorePluginsResponse) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *LoadMorePluginsResponse) GetNextOffset() int32 {
	if x != nil {
		return x.NextOffset
	}
	return 0
}

func (x *LoadMorePluginsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_proto_store_v1_service_proto protoreflect.FileDescriptor

const file_proto_store_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x1cproto/store/v1/service.proto\x12\x0eproto.store.v1\"\xad\x01\n" +
	"\x06Plugin\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x19\n" +
	"\bapp_name\x18\x02 \x01(\tR\aappName\x12!\n" +
	"\fpackage_name\x18\x03 \x01(\tR\vpackageName\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x12\x19\n" +
	"\bicon_url\x18\x05 \x01(\tR\aiconUrl\x12\x1e\n" +
	"\n" +
	"categories\x18\x06 \x03(\tR\n" +
	"categories\"\xb9\x02\n" +
	"\bDeeplink\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bdeeplink\x18\x03 \x01(\tR\bdeeplink\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x1a\n" +
	"\bverified\x18\x05 \x01(\bR\bverified\x12.\n" +
	"\x13requires_text_input\x18\x06 \x01(\bR\x11requiresTextInput\x12\"\n" +
	"\ropen_with_app\x18\a \x01(\tR\vopenWithApp\x12#\n" +
	"\rpreset_groups\x18\b \x03(\tR\fpresetGroups\x12,\n" +
	"\x12open_with_activity\x18\t \x01(\tR\x10openWithActivity\x12\x16\n" +
	"\x06config\x18\n" +
	" \x01(\tR\x06config\"v\n" +
	"\fPluginDetail\x12.\n" +
	"\x06plugin\x18\x01 \x01(\v2\x16.proto.store.v1.PluginR\x06plugin\x126\n" +
	"\tdeeplinks\x18\x02 \x03(\v2\x18.proto.store.v1.DeeplinkR\tdeeplinks\"\"\n" +
	"\x10GetPluginRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"I\n" +
	"\x11GetPluginResponse\x124\n" +
	"\x06plugin\x18\x01 \x01(\v2\x1c.proto.store.v1.PluginDetailR\x06plugin\"\x16\n" +
	"\x14GetCategoriesRequest\"7\n" +
	"\x15GetCategoriesResponse\x12\x1e\n" +
	"\n" +
	"categories\x18\x01 \x03(\tR\n" +
	"categories\"\x18\n" +
	"\x16GetPresetGroupsRequest\">\n" +
	"\x17GetPresetGroupsResponse\x12#\n" +
	"\rpreset_groups\x18\x01 \x03(\tR\fpresetGroups\"5\n" +
	"\x17GetPresetPluginsRequest\x12\x1a\n" +
	"\blanguage\x18\x01 \x01(\tR\blanguage\"R\n" +
	"\x18GetPresetPluginsResponse\x126\n" +
	"\aplugins\x18\x01 \x03(\v2\x1c.proto.store.v1.PluginDetailR\aplugins\"\xc2\x01\n" +
	"\x16LoadMorePluginsRequest\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x1a\n" +
	"\bcategory\x18\x03 \x01(\tR\bcategory\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x12!\n" +
	"\fpreset_group\x18\x05 \x01(\tR\vpresetGroup\x12\x1f\n" +
	"\vsearch_term\x18\x06 \x01(\tR\n" +
	"searchTerm\"\xae\x01\n" +
	"\x17LoadMorePluginsResponse\x126\n" +
	"\aplugins\x18\x01 \x03(\v2\x1c.proto.store.v1.PluginDetailR\aplugins\x12\x19\n" +
	"\bhas_more\x18\x02 \x01(\bR\ahasMore\x12\x1f\n" +
	"\vnext_offset\x18\x03 \x01(\x05R\n" +
	"nextOffset\x12\x1f\n" +
	"\vtotal_count\x18\x04 \x01(\x05R\n" +
	"totalCount2\xf7\x03\n" +
	"\fStoreService\x12R\n" +
	"\tGetPlugin\x12 .proto.store.v1.GetPluginRequest\x1a!.proto.store.v1.GetPluginResponse\"\x00\x12^\n" +
	"\rGetCategories\x12$.proto.store.v1.GetCategoriesRequest\x1a%.proto.store.v1.GetCategoriesResponse\"\x00\x12d\n" +
	"\x0fGetPresetGroups\x12&.proto.store.v1.GetPresetGroupsRequest\x1a'.proto.store.v1.GetPresetGroupsResponse\"\x00\x12g\n" +
	"\x10GetPresetPlugins\x12'.proto.store.v1.GetPresetPluginsRequest\x1a(.proto.store.v1.GetPresetPluginsResponse\"\x00\x12d\n" +
	"\x0fLoadMorePlugins\x12&.proto.store.v1.LoadMorePluginsRequest\x1a'.proto.store.v1.LoadMorePluginsResponse\"\x00B.Z,fastserver.com/fastserver/gen/proto/store/v1b\x06proto3"

var (
	file_proto_store_v1_service_proto_rawDescOnce sync.Once
	file_proto_store_v1_service_proto_rawDescData []byte
)

func file_proto_store_v1_service_proto_rawDescGZIP() []byte {
	file_proto_store_v1_service_proto_rawDescOnce.Do(func() {
		file_proto_store_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_store_v1_service_proto_rawDesc), len(file_proto_store_v1_service_proto_rawDesc)))
	})
	return file_proto_store_v1_service_proto_rawDescData
}

var file_proto_store_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_store_v1_service_proto_goTypes = []any{
	(*Plugin)(nil),                   // 0: proto.store.v1.Plugin
	(*Deeplink)(nil),                 // 1: proto.store.v1.Deeplink
	(*PluginDetail)(nil),             // 2: proto.store.v1.PluginDetail
	(*GetPluginRequest)(nil),         // 3: proto.store.v1.GetPluginRequest
	(*GetPluginResponse)(nil),        // 4: proto.store.v1.GetPluginResponse
	(*GetCategoriesRequest)(nil),     // 5: proto.store.v1.GetCategoriesRequest
	(*GetCategoriesResponse)(nil),    // 6: proto.store.v1.GetCategoriesResponse
	(*GetPresetGroupsRequest)(nil),   // 7: proto.store.v1.GetPresetGroupsRequest
	(*GetPresetGroupsResponse)(nil),  // 8: proto.store.v1.GetPresetGroupsResponse
	(*GetPresetPluginsRequest)(nil),  // 9: proto.store.v1.GetPresetPluginsRequest
	(*GetPresetPluginsResponse)(nil), // 10: proto.store.v1.GetPresetPluginsResponse
	(*LoadMorePluginsRequest)(nil),   // 11: proto.store.v1.LoadMorePluginsRequest
	(*LoadMorePluginsResponse)(nil),  // 12: proto.store.v1.LoadMorePluginsResponse
}
var file_proto_store_v1_service_proto_depIdxs = []int32{
	0,  // 0: proto.store.v1.PluginDetail.plugin:type_name -> proto.store.v1.Plugin
	1,  // 1: proto.store.v1.PluginDetail.deeplinks:type_name -> proto.store.v1.Deeplink
	2,  // 2: proto.store.v1.GetPluginResponse.plugin:type_name -> proto.store.v1.PluginDetail
	2,  // 3: proto.store.v1.GetPresetPluginsResponse.plugins:type_name -> proto.store.v1.PluginDetail
	2,  // 4: proto.store.v1.LoadMorePluginsResponse.plugins:type_name -> proto.store.v1.PluginDetail
	3,  // 5: proto.store.v1.StoreService.GetPlugin:input_type -> proto.store.v1.GetPluginRequest
	5,  // 6: proto.store.v1.StoreService.GetCategories:input_type -> proto.store.v1.GetCategoriesRequest
	7,  // 7: proto.store.v1.StoreService.GetPresetGroups:input_type -> proto.store.v1.GetPresetGroupsRequest
	9,  // 8: proto.store.v1.StoreService.GetPresetPlugins:input_type -> proto.store.v1.GetPresetPluginsRequest
	11, // 9: proto.store.v1.StoreService.LoadMorePlugins:input_type -> proto.store.v1.LoadMorePluginsRequest
	4,  // 10: proto.store.v1.StoreService.GetPlugin:output_type -> proto.store.v1.GetPluginResponse
	6,  // 11: proto.store.v1.StoreService.GetCategories:output_type -> proto.store.v1.GetCategoriesResponse
	8,  // 12: proto.store.v1.StoreService.GetPresetGroups:output_type -> proto.store.v1.GetPresetGroupsResponse
	10, // 13: proto.store.v1.StoreService.GetPresetPlugins:output_type -> proto.store.v1.GetPresetPluginsResponse
	12, // 14: proto.store.v1.StoreService.LoadMorePlugins:output_type -> proto.store.v1.LoadMorePluginsResponse
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_proto_store_v1_service_proto_init() }
func file_proto_store_v1_service_proto_init() {
	if File_proto_store_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_store_v1_service_proto_rawDesc), len(file_proto_store_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_store_v1_service_proto_goTypes,
		DependencyIndexes: file_proto_store_v1_service_proto_depIdxs,
		MessageInfos:      file_proto_store_v1_service_proto_msgTypes,
	}.Build()
	File_proto_store_v1_service_proto = out.File
	file_proto_store_v1_service_proto_goTypes = nil
	file_proto_store_v1_service_proto_depIdxs = nil
}
