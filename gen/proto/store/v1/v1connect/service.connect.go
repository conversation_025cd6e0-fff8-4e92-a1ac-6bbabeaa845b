// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proto/store/v1/service.proto

package v1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "fastserver.com/fastserver/gen/proto/store/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// StoreServiceName is the fully-qualified name of the StoreService service.
	StoreServiceName = "proto.store.v1.StoreService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// StoreServiceGetPluginProcedure is the fully-qualified name of the StoreService's GetPlugin RPC.
	StoreServiceGetPluginProcedure = "/proto.store.v1.StoreService/GetPlugin"
	// StoreServiceGetCategoriesProcedure is the fully-qualified name of the StoreService's
	// GetCategories RPC.
	StoreServiceGetCategoriesProcedure = "/proto.store.v1.StoreService/GetCategories"
	// StoreServiceGetPresetGroupsProcedure is the fully-qualified name of the StoreService's
	// GetPresetGroups RPC.
	StoreServiceGetPresetGroupsProcedure = "/proto.store.v1.StoreService/GetPresetGroups"
	// StoreServiceGetPresetPluginsProcedure is the fully-qualified name of the StoreService's
	// GetPresetPlugins RPC.
	StoreServiceGetPresetPluginsProcedure = "/proto.store.v1.StoreService/GetPresetPlugins"
	// StoreServiceLoadMorePluginsProcedure is the fully-qualified name of the StoreService's
	// LoadMorePlugins RPC.
	StoreServiceLoadMorePluginsProcedure = "/proto.store.v1.StoreService/LoadMorePlugins"
)

// StoreServiceClient is a client for the proto.store.v1.StoreService service.
type StoreServiceClient interface {
	// GetPlugin 获取单个插件的详细信息
	GetPlugin(context.Context, *connect.Request[v1.GetPluginRequest]) (*connect.Response[v1.GetPluginResponse], error)
	// GetCategories 获取所有可用的类别
	GetCategories(context.Context, *connect.Request[v1.GetCategoriesRequest]) (*connect.Response[v1.GetCategoriesResponse], error)
	// GetPresetGroups 获取所有可用的预设组
	GetPresetGroups(context.Context, *connect.Request[v1.GetPresetGroupsRequest]) (*connect.Response[v1.GetPresetGroupsResponse], error)
	// GetPresetPlugins 获取所有预设的插件
	GetPresetPlugins(context.Context, *connect.Request[v1.GetPresetPluginsRequest]) (*connect.Response[v1.GetPresetPluginsResponse], error)
	// LoadMorePlugins 加载更多插件，支持筛选
	LoadMorePlugins(context.Context, *connect.Request[v1.LoadMorePluginsRequest]) (*connect.Response[v1.LoadMorePluginsResponse], error)
}

// NewStoreServiceClient constructs a client for the proto.store.v1.StoreService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewStoreServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) StoreServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	storeServiceMethods := v1.File_proto_store_v1_service_proto.Services().ByName("StoreService").Methods()
	return &storeServiceClient{
		getPlugin: connect.NewClient[v1.GetPluginRequest, v1.GetPluginResponse](
			httpClient,
			baseURL+StoreServiceGetPluginProcedure,
			connect.WithSchema(storeServiceMethods.ByName("GetPlugin")),
			connect.WithClientOptions(opts...),
		),
		getCategories: connect.NewClient[v1.GetCategoriesRequest, v1.GetCategoriesResponse](
			httpClient,
			baseURL+StoreServiceGetCategoriesProcedure,
			connect.WithSchema(storeServiceMethods.ByName("GetCategories")),
			connect.WithClientOptions(opts...),
		),
		getPresetGroups: connect.NewClient[v1.GetPresetGroupsRequest, v1.GetPresetGroupsResponse](
			httpClient,
			baseURL+StoreServiceGetPresetGroupsProcedure,
			connect.WithSchema(storeServiceMethods.ByName("GetPresetGroups")),
			connect.WithClientOptions(opts...),
		),
		getPresetPlugins: connect.NewClient[v1.GetPresetPluginsRequest, v1.GetPresetPluginsResponse](
			httpClient,
			baseURL+StoreServiceGetPresetPluginsProcedure,
			connect.WithSchema(storeServiceMethods.ByName("GetPresetPlugins")),
			connect.WithClientOptions(opts...),
		),
		loadMorePlugins: connect.NewClient[v1.LoadMorePluginsRequest, v1.LoadMorePluginsResponse](
			httpClient,
			baseURL+StoreServiceLoadMorePluginsProcedure,
			connect.WithSchema(storeServiceMethods.ByName("LoadMorePlugins")),
			connect.WithClientOptions(opts...),
		),
	}
}

// storeServiceClient implements StoreServiceClient.
type storeServiceClient struct {
	getPlugin        *connect.Client[v1.GetPluginRequest, v1.GetPluginResponse]
	getCategories    *connect.Client[v1.GetCategoriesRequest, v1.GetCategoriesResponse]
	getPresetGroups  *connect.Client[v1.GetPresetGroupsRequest, v1.GetPresetGroupsResponse]
	getPresetPlugins *connect.Client[v1.GetPresetPluginsRequest, v1.GetPresetPluginsResponse]
	loadMorePlugins  *connect.Client[v1.LoadMorePluginsRequest, v1.LoadMorePluginsResponse]
}

// GetPlugin calls proto.store.v1.StoreService.GetPlugin.
func (c *storeServiceClient) GetPlugin(ctx context.Context, req *connect.Request[v1.GetPluginRequest]) (*connect.Response[v1.GetPluginResponse], error) {
	return c.getPlugin.CallUnary(ctx, req)
}

// GetCategories calls proto.store.v1.StoreService.GetCategories.
func (c *storeServiceClient) GetCategories(ctx context.Context, req *connect.Request[v1.GetCategoriesRequest]) (*connect.Response[v1.GetCategoriesResponse], error) {
	return c.getCategories.CallUnary(ctx, req)
}

// GetPresetGroups calls proto.store.v1.StoreService.GetPresetGroups.
func (c *storeServiceClient) GetPresetGroups(ctx context.Context, req *connect.Request[v1.GetPresetGroupsRequest]) (*connect.Response[v1.GetPresetGroupsResponse], error) {
	return c.getPresetGroups.CallUnary(ctx, req)
}

// GetPresetPlugins calls proto.store.v1.StoreService.GetPresetPlugins.
func (c *storeServiceClient) GetPresetPlugins(ctx context.Context, req *connect.Request[v1.GetPresetPluginsRequest]) (*connect.Response[v1.GetPresetPluginsResponse], error) {
	return c.getPresetPlugins.CallUnary(ctx, req)
}

// LoadMorePlugins calls proto.store.v1.StoreService.LoadMorePlugins.
func (c *storeServiceClient) LoadMorePlugins(ctx context.Context, req *connect.Request[v1.LoadMorePluginsRequest]) (*connect.Response[v1.LoadMorePluginsResponse], error) {
	return c.loadMorePlugins.CallUnary(ctx, req)
}

// StoreServiceHandler is an implementation of the proto.store.v1.StoreService service.
type StoreServiceHandler interface {
	// GetPlugin 获取单个插件的详细信息
	GetPlugin(context.Context, *connect.Request[v1.GetPluginRequest]) (*connect.Response[v1.GetPluginResponse], error)
	// GetCategories 获取所有可用的类别
	GetCategories(context.Context, *connect.Request[v1.GetCategoriesRequest]) (*connect.Response[v1.GetCategoriesResponse], error)
	// GetPresetGroups 获取所有可用的预设组
	GetPresetGroups(context.Context, *connect.Request[v1.GetPresetGroupsRequest]) (*connect.Response[v1.GetPresetGroupsResponse], error)
	// GetPresetPlugins 获取所有预设的插件
	GetPresetPlugins(context.Context, *connect.Request[v1.GetPresetPluginsRequest]) (*connect.Response[v1.GetPresetPluginsResponse], error)
	// LoadMorePlugins 加载更多插件，支持筛选
	LoadMorePlugins(context.Context, *connect.Request[v1.LoadMorePluginsRequest]) (*connect.Response[v1.LoadMorePluginsResponse], error)
}

// NewStoreServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewStoreServiceHandler(svc StoreServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	storeServiceMethods := v1.File_proto_store_v1_service_proto.Services().ByName("StoreService").Methods()
	storeServiceGetPluginHandler := connect.NewUnaryHandler(
		StoreServiceGetPluginProcedure,
		svc.GetPlugin,
		connect.WithSchema(storeServiceMethods.ByName("GetPlugin")),
		connect.WithHandlerOptions(opts...),
	)
	storeServiceGetCategoriesHandler := connect.NewUnaryHandler(
		StoreServiceGetCategoriesProcedure,
		svc.GetCategories,
		connect.WithSchema(storeServiceMethods.ByName("GetCategories")),
		connect.WithHandlerOptions(opts...),
	)
	storeServiceGetPresetGroupsHandler := connect.NewUnaryHandler(
		StoreServiceGetPresetGroupsProcedure,
		svc.GetPresetGroups,
		connect.WithSchema(storeServiceMethods.ByName("GetPresetGroups")),
		connect.WithHandlerOptions(opts...),
	)
	storeServiceGetPresetPluginsHandler := connect.NewUnaryHandler(
		StoreServiceGetPresetPluginsProcedure,
		svc.GetPresetPlugins,
		connect.WithSchema(storeServiceMethods.ByName("GetPresetPlugins")),
		connect.WithHandlerOptions(opts...),
	)
	storeServiceLoadMorePluginsHandler := connect.NewUnaryHandler(
		StoreServiceLoadMorePluginsProcedure,
		svc.LoadMorePlugins,
		connect.WithSchema(storeServiceMethods.ByName("LoadMorePlugins")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proto.store.v1.StoreService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case StoreServiceGetPluginProcedure:
			storeServiceGetPluginHandler.ServeHTTP(w, r)
		case StoreServiceGetCategoriesProcedure:
			storeServiceGetCategoriesHandler.ServeHTTP(w, r)
		case StoreServiceGetPresetGroupsProcedure:
			storeServiceGetPresetGroupsHandler.ServeHTTP(w, r)
		case StoreServiceGetPresetPluginsProcedure:
			storeServiceGetPresetPluginsHandler.ServeHTTP(w, r)
		case StoreServiceLoadMorePluginsProcedure:
			storeServiceLoadMorePluginsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedStoreServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedStoreServiceHandler struct{}

func (UnimplementedStoreServiceHandler) GetPlugin(context.Context, *connect.Request[v1.GetPluginRequest]) (*connect.Response[v1.GetPluginResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.store.v1.StoreService.GetPlugin is not implemented"))
}

func (UnimplementedStoreServiceHandler) GetCategories(context.Context, *connect.Request[v1.GetCategoriesRequest]) (*connect.Response[v1.GetCategoriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.store.v1.StoreService.GetCategories is not implemented"))
}

func (UnimplementedStoreServiceHandler) GetPresetGroups(context.Context, *connect.Request[v1.GetPresetGroupsRequest]) (*connect.Response[v1.GetPresetGroupsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.store.v1.StoreService.GetPresetGroups is not implemented"))
}

func (UnimplementedStoreServiceHandler) GetPresetPlugins(context.Context, *connect.Request[v1.GetPresetPluginsRequest]) (*connect.Response[v1.GetPresetPluginsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.store.v1.StoreService.GetPresetPlugins is not implemented"))
}

func (UnimplementedStoreServiceHandler) LoadMorePlugins(context.Context, *connect.Request[v1.LoadMorePluginsRequest]) (*connect.Response[v1.LoadMorePluginsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.store.v1.StoreService.LoadMorePlugins is not implemented"))
}
