// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/task/v1/service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RepeatIntervalUnit int32

const (
	RepeatIntervalUnit_REPEAT_INTERVAL_UNIT_UNSPECIFIED RepeatIntervalUnit = 0
	RepeatIntervalUnit_DAY                              RepeatIntervalUnit = 1
	RepeatIntervalUnit_WEEK                             RepeatIntervalUnit = 2
	RepeatIntervalUnit_MONTH                            RepeatIntervalUnit = 3
	RepeatIntervalUnit_YEAR                             RepeatIntervalUnit = 4
)

// Enum value maps for RepeatIntervalUnit.
var (
	RepeatIntervalUnit_name = map[int32]string{
		0: "REPEAT_INTERVAL_UNIT_UNSPECIFIED",
		1: "DAY",
		2: "WEEK",
		3: "MONTH",
		4: "YEAR",
	}
	RepeatIntervalUnit_value = map[string]int32{
		"REPEAT_INTERVAL_UNIT_UNSPECIFIED": 0,
		"DAY":                              1,
		"WEEK":                             2,
		"MONTH":                            3,
		"YEAR":                             4,
	}
)

func (x RepeatIntervalUnit) Enum() *RepeatIntervalUnit {
	p := new(RepeatIntervalUnit)
	*p = x
	return p
}

func (x RepeatIntervalUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepeatIntervalUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_task_v1_service_proto_enumTypes[0].Descriptor()
}

func (RepeatIntervalUnit) Type() protoreflect.EnumType {
	return &file_proto_task_v1_service_proto_enumTypes[0]
}

func (x RepeatIntervalUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepeatIntervalUnit.Descriptor instead.
func (RepeatIntervalUnit) EnumDescriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{0}
}

// AudioFormat 定义支持的音频格式
type AudioFormat int32

const (
	AudioFormat_AUDIO_FORMAT_UNSPECIFIED AudioFormat = 0
	AudioFormat_MP3                      AudioFormat = 1
	AudioFormat_WAV                      AudioFormat = 2
	AudioFormat_M4A                      AudioFormat = 3
	AudioFormat_OGG                      AudioFormat = 4
	AudioFormat_FLAC                     AudioFormat = 5
	AudioFormat_PCM                      AudioFormat = 6
)

// Enum value maps for AudioFormat.
var (
	AudioFormat_name = map[int32]string{
		0: "AUDIO_FORMAT_UNSPECIFIED",
		1: "MP3",
		2: "WAV",
		3: "M4A",
		4: "OGG",
		5: "FLAC",
		6: "PCM",
	}
	AudioFormat_value = map[string]int32{
		"AUDIO_FORMAT_UNSPECIFIED": 0,
		"MP3":                      1,
		"WAV":                      2,
		"M4A":                      3,
		"OGG":                      4,
		"FLAC":                     5,
		"PCM":                      6,
	}
)

func (x AudioFormat) Enum() *AudioFormat {
	p := new(AudioFormat)
	*p = x
	return p
}

func (x AudioFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AudioFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_task_v1_service_proto_enumTypes[1].Descriptor()
}

func (AudioFormat) Type() protoreflect.EnumType {
	return &file_proto_task_v1_service_proto_enumTypes[1]
}

func (x AudioFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AudioFormat.Descriptor instead.
func (AudioFormat) EnumDescriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{1}
}

// SimpleTask 定义了一个基础任务的结构
type SimpleTask struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                    // 任务唯一标识符
	Name                string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                                                // 任务名称
	Plugin              string                 `protobuf:"bytes,3,opt,name=plugin,proto3" json:"plugin,omitempty"`                                                                                            // 插件名称（对应的APP）
	Search              string                 `protobuf:"bytes,4,opt,name=search,proto3" json:"search,omitempty"`                                                                                            // 搜索内容
	StartTime           *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                                                                     // 首次执行时间
	Repeatable          bool                   `protobuf:"varint,6,opt,name=repeatable,proto3" json:"repeatable,omitempty"`                                                                                   // 是否为重复任务
	RepeatTimes         int32                  `protobuf:"varint,7,opt,name=repeat_times,json=repeatTimes,proto3" json:"repeat_times,omitempty"`                                                              // 重复次数
	RepeatIntervalValue int32                  `protobuf:"varint,8,opt,name=repeat_interval_value,json=repeatIntervalValue,proto3" json:"repeat_interval_value,omitempty"`                                    // 重复周期值
	RepeatIntervalUnit  RepeatIntervalUnit     `protobuf:"varint,9,opt,name=repeat_interval_unit,json=repeatIntervalUnit,proto3,enum=proto.task.v1.RepeatIntervalUnit" json:"repeat_interval_unit,omitempty"` // 重复周期单位
	Description         string                 `protobuf:"bytes,10,opt,name=description,proto3" json:"description,omitempty"`                                                                                 // 任务描述
	Timezone            string                 `protobuf:"bytes,11,opt,name=timezone,proto3" json:"timezone,omitempty"`                                                                                       // 时区信息
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SimpleTask) Reset() {
	*x = SimpleTask{}
	mi := &file_proto_task_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimpleTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleTask) ProtoMessage() {}

func (x *SimpleTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_task_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleTask.ProtoReflect.Descriptor instead.
func (*SimpleTask) Descriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *SimpleTask) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SimpleTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimpleTask) GetPlugin() string {
	if x != nil {
		return x.Plugin
	}
	return ""
}

func (x *SimpleTask) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *SimpleTask) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *SimpleTask) GetRepeatable() bool {
	if x != nil {
		return x.Repeatable
	}
	return false
}

func (x *SimpleTask) GetRepeatTimes() int32 {
	if x != nil {
		return x.RepeatTimes
	}
	return 0
}

func (x *SimpleTask) GetRepeatIntervalValue() int32 {
	if x != nil {
		return x.RepeatIntervalValue
	}
	return 0
}

func (x *SimpleTask) GetRepeatIntervalUnit() RepeatIntervalUnit {
	if x != nil {
		return x.RepeatIntervalUnit
	}
	return RepeatIntervalUnit_REPEAT_INTERVAL_UNIT_UNSPECIFIED
}

func (x *SimpleTask) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SimpleTask) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

// TokenUsage 定义了 token 使用情况的详细结构
type TokenUsage struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PromptTokens     int32                  `protobuf:"varint,1,opt,name=prompt_tokens,json=promptTokens,proto3" json:"prompt_tokens,omitempty"`             // 提示词使用的 token 数量
	CompletionTokens int32                  `protobuf:"varint,2,opt,name=completion_tokens,json=completionTokens,proto3" json:"completion_tokens,omitempty"` // 完成使用的 token 数量
	TotalTokens      int32                  `protobuf:"varint,3,opt,name=total_tokens,json=totalTokens,proto3" json:"total_tokens,omitempty"`                // 总 token 数量
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TokenUsage) Reset() {
	*x = TokenUsage{}
	mi := &file_proto_task_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenUsage) ProtoMessage() {}

func (x *TokenUsage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_task_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenUsage.ProtoReflect.Descriptor instead.
func (*TokenUsage) Descriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *TokenUsage) GetPromptTokens() int32 {
	if x != nil {
		return x.PromptTokens
	}
	return 0
}

func (x *TokenUsage) GetCompletionTokens() int32 {
	if x != nil {
		return x.CompletionTokens
	}
	return 0
}

func (x *TokenUsage) GetTotalTokens() int32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

// AnalyzeRequest 定义了分析任务的请求结构
type AnalyzeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Input         string                 `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"` // 用户输入的任务描述文本
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyzeRequest) Reset() {
	*x = AnalyzeRequest{}
	mi := &file_proto_task_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeRequest) ProtoMessage() {}

func (x *AnalyzeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_task_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeRequest.ProtoReflect.Descriptor instead.
func (*AnalyzeRequest) Descriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *AnalyzeRequest) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

// AnalyzeResponse 定义了分析任务的响应结构
type AnalyzeResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Task               *SimpleTask            `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`                                                         // 解析后的任务信息
	Methods            string                 `protobuf:"bytes,2,opt,name=methods,proto3" json:"methods,omitempty"`                                                   // 解析方法
	Error              string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`                                                       // 错误信息，如果解析失败则不为空
	DetailedTokenUsage *TokenUsage            `protobuf:"bytes,4,opt,name=detailed_token_usage,json=detailedTokenUsage,proto3" json:"detailed_token_usage,omitempty"` // 详细的 token 使用情况
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AnalyzeResponse) Reset() {
	*x = AnalyzeResponse{}
	mi := &file_proto_task_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzeResponse) ProtoMessage() {}

func (x *AnalyzeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_task_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzeResponse.ProtoReflect.Descriptor instead.
func (*AnalyzeResponse) Descriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *AnalyzeResponse) GetTask() *SimpleTask {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *AnalyzeResponse) GetMethods() string {
	if x != nil {
		return x.Methods
	}
	return ""
}

func (x *AnalyzeResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *AnalyzeResponse) GetDetailedTokenUsage() *TokenUsage {
	if x != nil {
		return x.DetailedTokenUsage
	}
	return nil
}

// ParseVoiceTaskRequest 定义语音任务解析的请求结构
type ParseVoiceTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 音频数据，base64编码
	AudioData string `protobuf:"bytes,1,opt,name=audio_data,json=audioData,proto3" json:"audio_data,omitempty"`
	// 音频格式
	AudioFormat AudioFormat `protobuf:"varint,2,opt,name=audio_format,json=audioFormat,proto3,enum=proto.task.v1.AudioFormat" json:"audio_format,omitempty"`
	// 可选：语言代码 (如 "zh-CN", "en-US")
	LanguageCode string `protobuf:"bytes,3,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"`
	// 可选：用户已安装的应用列表，用于提升任务解析准确性
	InstalledApps []string `protobuf:"bytes,4,rep,name=installed_apps,json=installedApps,proto3" json:"installed_apps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseVoiceTaskRequest) Reset() {
	*x = ParseVoiceTaskRequest{}
	mi := &file_proto_task_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseVoiceTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseVoiceTaskRequest) ProtoMessage() {}

func (x *ParseVoiceTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_task_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseVoiceTaskRequest.ProtoReflect.Descriptor instead.
func (*ParseVoiceTaskRequest) Descriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *ParseVoiceTaskRequest) GetAudioData() string {
	if x != nil {
		return x.AudioData
	}
	return ""
}

func (x *ParseVoiceTaskRequest) GetAudioFormat() AudioFormat {
	if x != nil {
		return x.AudioFormat
	}
	return AudioFormat_AUDIO_FORMAT_UNSPECIFIED
}

func (x *ParseVoiceTaskRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *ParseVoiceTaskRequest) GetInstalledApps() []string {
	if x != nil {
		return x.InstalledApps
	}
	return nil
}

// ParseVoiceTaskResponse 定义语音任务解析的响应结构
type ParseVoiceTaskResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 解析的任务信息
	Task *SimpleTask `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	// 转录的原始文本
	TranscribedText string `protobuf:"bytes,2,opt,name=transcribed_text,json=transcribedText,proto3" json:"transcribed_text,omitempty"`
	// 错误信息，如果解析失败则不为空
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	// Token 使用情况
	TokenUsage *TokenUsage `protobuf:"bytes,4,opt,name=token_usage,json=tokenUsage,proto3" json:"token_usage,omitempty"`
	// 解析完成时间
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	// 使用的模型名称
	ModelUsed string `protobuf:"bytes,6,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	// 音频时长（秒）
	AudioDuration float32 `protobuf:"fixed32,7,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseVoiceTaskResponse) Reset() {
	*x = ParseVoiceTaskResponse{}
	mi := &file_proto_task_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseVoiceTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseVoiceTaskResponse) ProtoMessage() {}

func (x *ParseVoiceTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_task_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseVoiceTaskResponse.ProtoReflect.Descriptor instead.
func (*ParseVoiceTaskResponse) Descriptor() ([]byte, []int) {
	return file_proto_task_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *ParseVoiceTaskResponse) GetTask() *SimpleTask {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *ParseVoiceTaskResponse) GetTranscribedText() string {
	if x != nil {
		return x.TranscribedText
	}
	return ""
}

func (x *ParseVoiceTaskResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *ParseVoiceTaskResponse) GetTokenUsage() *TokenUsage {
	if x != nil {
		return x.TokenUsage
	}
	return nil
}

func (x *ParseVoiceTaskResponse) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *ParseVoiceTaskResponse) GetModelUsed() string {
	if x != nil {
		return x.ModelUsed
	}
	return ""
}

func (x *ParseVoiceTaskResponse) GetAudioDuration() float32 {
	if x != nil {
		return x.AudioDuration
	}
	return 0
}

var File_proto_task_v1_service_proto protoreflect.FileDescriptor

const file_proto_task_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x1bproto/task/v1/service.proto\x12\rproto.task.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xa5\x03\n" +
	"\n" +
	"SimpleTask\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06plugin\x18\x03 \x01(\tR\x06plugin\x12\x16\n" +
	"\x06search\x18\x04 \x01(\tR\x06search\x129\n" +
	"\n" +
	"start_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12\x1e\n" +
	"\n" +
	"repeatable\x18\x06 \x01(\bR\n" +
	"repeatable\x12!\n" +
	"\frepeat_times\x18\a \x01(\x05R\vrepeatTimes\x122\n" +
	"\x15repeat_interval_value\x18\b \x01(\x05R\x13repeatIntervalValue\x12S\n" +
	"\x14repeat_interval_unit\x18\t \x01(\x0e2!.proto.task.v1.RepeatIntervalUnitR\x12repeatIntervalUnit\x12 \n" +
	"\vdescription\x18\n" +
	" \x01(\tR\vdescription\x12\x1a\n" +
	"\btimezone\x18\v \x01(\tR\btimezone\"\x81\x01\n" +
	"\n" +
	"TokenUsage\x12#\n" +
	"\rprompt_tokens\x18\x01 \x01(\x05R\fpromptTokens\x12+\n" +
	"\x11completion_tokens\x18\x02 \x01(\x05R\x10completionTokens\x12!\n" +
	"\ftotal_tokens\x18\x03 \x01(\x05R\vtotalTokens\"&\n" +
	"\x0eAnalyzeRequest\x12\x14\n" +
	"\x05input\x18\x01 \x01(\tR\x05input\"\xbd\x01\n" +
	"\x0fAnalyzeResponse\x12-\n" +
	"\x04task\x18\x01 \x01(\v2\x19.proto.task.v1.SimpleTaskR\x04task\x12\x18\n" +
	"\amethods\x18\x02 \x01(\tR\amethods\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\x12K\n" +
	"\x14detailed_token_usage\x18\x04 \x01(\v2\x19.proto.task.v1.TokenUsageR\x12detailedTokenUsage\"\xc1\x01\n" +
	"\x15ParseVoiceTaskRequest\x12\x1d\n" +
	"\n" +
	"audio_data\x18\x01 \x01(\tR\taudioData\x12=\n" +
	"\faudio_format\x18\x02 \x01(\x0e2\x1a.proto.task.v1.AudioFormatR\vaudioFormat\x12#\n" +
	"\rlanguage_code\x18\x03 \x01(\tR\flanguageCode\x12%\n" +
	"\x0einstalled_apps\x18\x04 \x03(\tR\rinstalledApps\"\xc9\x02\n" +
	"\x16ParseVoiceTaskResponse\x12-\n" +
	"\x04task\x18\x01 \x01(\v2\x19.proto.task.v1.SimpleTaskR\x04task\x12)\n" +
	"\x10transcribed_text\x18\x02 \x01(\tR\x0ftranscribedText\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\x12:\n" +
	"\vtoken_usage\x18\x04 \x01(\v2\x19.proto.task.v1.TokenUsageR\n" +
	"tokenUsage\x12=\n" +
	"\fcompleted_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x12\x1d\n" +
	"\n" +
	"model_used\x18\x06 \x01(\tR\tmodelUsed\x12%\n" +
	"\x0eaudio_duration\x18\a \x01(\x02R\raudioDuration*b\n" +
	"\x12RepeatIntervalUnit\x12$\n" +
	" REPEAT_INTERVAL_UNIT_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03DAY\x10\x01\x12\b\n" +
	"\x04WEEK\x10\x02\x12\t\n" +
	"\x05MONTH\x10\x03\x12\b\n" +
	"\x04YEAR\x10\x04*b\n" +
	"\vAudioFormat\x12\x1c\n" +
	"\x18AUDIO_FORMAT_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03MP3\x10\x01\x12\a\n" +
	"\x03WAV\x10\x02\x12\a\n" +
	"\x03M4A\x10\x03\x12\a\n" +
	"\x03OGG\x10\x04\x12\b\n" +
	"\x04FLAC\x10\x05\x12\a\n" +
	"\x03PCM\x10\x062\xba\x01\n" +
	"\vTaskService\x12J\n" +
	"\aAnalyze\x12\x1d.proto.task.v1.AnalyzeRequest\x1a\x1e.proto.task.v1.AnalyzeResponse\"\x00\x12_\n" +
	"\x0eParseVoiceTask\x12$.proto.task.v1.ParseVoiceTaskRequest\x1a%.proto.task.v1.ParseVoiceTaskResponse\"\x00B-Z+fastserver.com/fastserver/gen/proto/task/v1b\x06proto3"

var (
	file_proto_task_v1_service_proto_rawDescOnce sync.Once
	file_proto_task_v1_service_proto_rawDescData []byte
)

func file_proto_task_v1_service_proto_rawDescGZIP() []byte {
	file_proto_task_v1_service_proto_rawDescOnce.Do(func() {
		file_proto_task_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_task_v1_service_proto_rawDesc), len(file_proto_task_v1_service_proto_rawDesc)))
	})
	return file_proto_task_v1_service_proto_rawDescData
}

var file_proto_task_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_task_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_task_v1_service_proto_goTypes = []any{
	(RepeatIntervalUnit)(0),        // 0: proto.task.v1.RepeatIntervalUnit
	(AudioFormat)(0),               // 1: proto.task.v1.AudioFormat
	(*SimpleTask)(nil),             // 2: proto.task.v1.SimpleTask
	(*TokenUsage)(nil),             // 3: proto.task.v1.TokenUsage
	(*AnalyzeRequest)(nil),         // 4: proto.task.v1.AnalyzeRequest
	(*AnalyzeResponse)(nil),        // 5: proto.task.v1.AnalyzeResponse
	(*ParseVoiceTaskRequest)(nil),  // 6: proto.task.v1.ParseVoiceTaskRequest
	(*ParseVoiceTaskResponse)(nil), // 7: proto.task.v1.ParseVoiceTaskResponse
	(*timestamppb.Timestamp)(nil),  // 8: google.protobuf.Timestamp
}
var file_proto_task_v1_service_proto_depIdxs = []int32{
	8,  // 0: proto.task.v1.SimpleTask.start_time:type_name -> google.protobuf.Timestamp
	0,  // 1: proto.task.v1.SimpleTask.repeat_interval_unit:type_name -> proto.task.v1.RepeatIntervalUnit
	2,  // 2: proto.task.v1.AnalyzeResponse.task:type_name -> proto.task.v1.SimpleTask
	3,  // 3: proto.task.v1.AnalyzeResponse.detailed_token_usage:type_name -> proto.task.v1.TokenUsage
	1,  // 4: proto.task.v1.ParseVoiceTaskRequest.audio_format:type_name -> proto.task.v1.AudioFormat
	2,  // 5: proto.task.v1.ParseVoiceTaskResponse.task:type_name -> proto.task.v1.SimpleTask
	3,  // 6: proto.task.v1.ParseVoiceTaskResponse.token_usage:type_name -> proto.task.v1.TokenUsage
	8,  // 7: proto.task.v1.ParseVoiceTaskResponse.completed_at:type_name -> google.protobuf.Timestamp
	4,  // 8: proto.task.v1.TaskService.Analyze:input_type -> proto.task.v1.AnalyzeRequest
	6,  // 9: proto.task.v1.TaskService.ParseVoiceTask:input_type -> proto.task.v1.ParseVoiceTaskRequest
	5,  // 10: proto.task.v1.TaskService.Analyze:output_type -> proto.task.v1.AnalyzeResponse
	7,  // 11: proto.task.v1.TaskService.ParseVoiceTask:output_type -> proto.task.v1.ParseVoiceTaskResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_proto_task_v1_service_proto_init() }
func file_proto_task_v1_service_proto_init() {
	if File_proto_task_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_task_v1_service_proto_rawDesc), len(file_proto_task_v1_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_task_v1_service_proto_goTypes,
		DependencyIndexes: file_proto_task_v1_service_proto_depIdxs,
		EnumInfos:         file_proto_task_v1_service_proto_enumTypes,
		MessageInfos:      file_proto_task_v1_service_proto_msgTypes,
	}.Build()
	File_proto_task_v1_service_proto = out.File
	file_proto_task_v1_service_proto_goTypes = nil
	file_proto_task_v1_service_proto_depIdxs = nil
}
