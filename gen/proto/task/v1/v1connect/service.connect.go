// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proto/task/v1/service.proto

package v1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "fastserver.com/fastserver/gen/proto/task/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// TaskServiceName is the fully-qualified name of the TaskService service.
	TaskServiceName = "proto.task.v1.TaskService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// TaskServiceAnalyzeProcedure is the fully-qualified name of the TaskService's Analyze RPC.
	TaskServiceAnalyzeProcedure = "/proto.task.v1.TaskService/Analyze"
	// TaskServiceParseVoiceTaskProcedure is the fully-qualified name of the TaskService's
	// ParseVoiceTask RPC.
	TaskServiceParseVoiceTaskProcedure = "/proto.task.v1.TaskService/ParseVoiceTask"
)

// TaskServiceClient is a client for the proto.task.v1.TaskService service.
type TaskServiceClient interface {
	// Analyze 解析用户输入的任务描述，返回结构化的任务信息
	Analyze(context.Context, *connect.Request[v1.AnalyzeRequest]) (*connect.Response[v1.AnalyzeResponse], error)
	// ParseVoiceTask 解析用户语音中的任务信息
	ParseVoiceTask(context.Context, *connect.Request[v1.ParseVoiceTaskRequest]) (*connect.Response[v1.ParseVoiceTaskResponse], error)
}

// NewTaskServiceClient constructs a client for the proto.task.v1.TaskService service. By default,
// it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and
// sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC()
// or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewTaskServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) TaskServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	taskServiceMethods := v1.File_proto_task_v1_service_proto.Services().ByName("TaskService").Methods()
	return &taskServiceClient{
		analyze: connect.NewClient[v1.AnalyzeRequest, v1.AnalyzeResponse](
			httpClient,
			baseURL+TaskServiceAnalyzeProcedure,
			connect.WithSchema(taskServiceMethods.ByName("Analyze")),
			connect.WithClientOptions(opts...),
		),
		parseVoiceTask: connect.NewClient[v1.ParseVoiceTaskRequest, v1.ParseVoiceTaskResponse](
			httpClient,
			baseURL+TaskServiceParseVoiceTaskProcedure,
			connect.WithSchema(taskServiceMethods.ByName("ParseVoiceTask")),
			connect.WithClientOptions(opts...),
		),
	}
}

// taskServiceClient implements TaskServiceClient.
type taskServiceClient struct {
	analyze        *connect.Client[v1.AnalyzeRequest, v1.AnalyzeResponse]
	parseVoiceTask *connect.Client[v1.ParseVoiceTaskRequest, v1.ParseVoiceTaskResponse]
}

// Analyze calls proto.task.v1.TaskService.Analyze.
func (c *taskServiceClient) Analyze(ctx context.Context, req *connect.Request[v1.AnalyzeRequest]) (*connect.Response[v1.AnalyzeResponse], error) {
	return c.analyze.CallUnary(ctx, req)
}

// ParseVoiceTask calls proto.task.v1.TaskService.ParseVoiceTask.
func (c *taskServiceClient) ParseVoiceTask(ctx context.Context, req *connect.Request[v1.ParseVoiceTaskRequest]) (*connect.Response[v1.ParseVoiceTaskResponse], error) {
	return c.parseVoiceTask.CallUnary(ctx, req)
}

// TaskServiceHandler is an implementation of the proto.task.v1.TaskService service.
type TaskServiceHandler interface {
	// Analyze 解析用户输入的任务描述，返回结构化的任务信息
	Analyze(context.Context, *connect.Request[v1.AnalyzeRequest]) (*connect.Response[v1.AnalyzeResponse], error)
	// ParseVoiceTask 解析用户语音中的任务信息
	ParseVoiceTask(context.Context, *connect.Request[v1.ParseVoiceTaskRequest]) (*connect.Response[v1.ParseVoiceTaskResponse], error)
}

// NewTaskServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewTaskServiceHandler(svc TaskServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	taskServiceMethods := v1.File_proto_task_v1_service_proto.Services().ByName("TaskService").Methods()
	taskServiceAnalyzeHandler := connect.NewUnaryHandler(
		TaskServiceAnalyzeProcedure,
		svc.Analyze,
		connect.WithSchema(taskServiceMethods.ByName("Analyze")),
		connect.WithHandlerOptions(opts...),
	)
	taskServiceParseVoiceTaskHandler := connect.NewUnaryHandler(
		TaskServiceParseVoiceTaskProcedure,
		svc.ParseVoiceTask,
		connect.WithSchema(taskServiceMethods.ByName("ParseVoiceTask")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proto.task.v1.TaskService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case TaskServiceAnalyzeProcedure:
			taskServiceAnalyzeHandler.ServeHTTP(w, r)
		case TaskServiceParseVoiceTaskProcedure:
			taskServiceParseVoiceTaskHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedTaskServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedTaskServiceHandler struct{}

func (UnimplementedTaskServiceHandler) Analyze(context.Context, *connect.Request[v1.AnalyzeRequest]) (*connect.Response[v1.AnalyzeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.task.v1.TaskService.Analyze is not implemented"))
}

func (UnimplementedTaskServiceHandler) ParseVoiceTask(context.Context, *connect.Request[v1.ParseVoiceTaskRequest]) (*connect.Response[v1.ParseVoiceTaskResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.task.v1.TaskService.ParseVoiceTask is not implemented"))
}
