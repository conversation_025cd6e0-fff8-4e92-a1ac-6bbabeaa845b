// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/user/v1/service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Avatar        string                 `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     string                 `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	FirebaseUid   *string                `protobuf:"bytes,7,opt,name=firebase_uid,json=firebaseUid,proto3,oneof" json:"firebase_uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_proto_user_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *User) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *User) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *User) GetFirebaseUid() string {
	if x != nil && x.FirebaseUid != nil {
		return *x.FirebaseUid
	}
	return ""
}

type GetUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Identifier:
	//
	//	*GetUserRequest_Id
	//	*GetUserRequest_FirebaseUid
	Identifier    isGetUserRequest_Identifier `protobuf_oneof:"identifier"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_proto_user_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserRequest) GetIdentifier() isGetUserRequest_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *GetUserRequest) GetId() string {
	if x != nil {
		if x, ok := x.Identifier.(*GetUserRequest_Id); ok {
			return x.Id
		}
	}
	return ""
}

func (x *GetUserRequest) GetFirebaseUid() string {
	if x != nil {
		if x, ok := x.Identifier.(*GetUserRequest_FirebaseUid); ok {
			return x.FirebaseUid
		}
	}
	return ""
}

type isGetUserRequest_Identifier interface {
	isGetUserRequest_Identifier()
}

type GetUserRequest_Id struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3,oneof"`
}

type GetUserRequest_FirebaseUid struct {
	FirebaseUid string `protobuf:"bytes,2,opt,name=firebase_uid,json=firebaseUid,proto3,oneof"`
}

func (*GetUserRequest_Id) isGetUserRequest_Identifier() {}

func (*GetUserRequest_FirebaseUid) isGetUserRequest_Identifier() {}

type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_proto_user_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type LoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FirebaseUid   string                 `protobuf:"bytes,1,opt,name=firebase_uid,json=firebaseUid,proto3" json:"firebase_uid,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	mi := &file_proto_user_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *LoginRequest) GetFirebaseUid() string {
	if x != nil {
		return x.FirebaseUid
	}
	return ""
}

func (x *LoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type LoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResponse) Reset() {
	*x = LoginResponse{}
	mi := &file_proto_user_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResponse) ProtoMessage() {}

func (x *LoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResponse.ProtoReflect.Descriptor instead.
func (*LoginResponse) Descriptor() ([]byte, []int) {
	return file_proto_user_v1_service_proto_rawDescGZIP(), []int{4}
}

var File_proto_user_v1_service_proto protoreflect.FileDescriptor

const file_proto_user_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x1bproto/user/v1/service.proto\x12\rproto.user.v1\"\xcf\x01\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x16\n" +
	"\x06avatar\x18\x04 \x01(\tR\x06avatar\x12\x1d\n" +
	"\n" +
	"created_at\x18\x05 \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\tR\tupdatedAt\x12&\n" +
	"\ffirebase_uid\x18\a \x01(\tH\x00R\vfirebaseUid\x88\x01\x01B\x0f\n" +
	"\r_firebase_uid\"U\n" +
	"\x0eGetUserRequest\x12\x10\n" +
	"\x02id\x18\x01 \x01(\tH\x00R\x02id\x12#\n" +
	"\ffirebase_uid\x18\x02 \x01(\tH\x00R\vfirebaseUidB\f\n" +
	"\n" +
	"identifier\":\n" +
	"\x0fGetUserResponse\x12'\n" +
	"\x04user\x18\x01 \x01(\v2\x13.proto.user.v1.UserR\x04user\"G\n" +
	"\fLoginRequest\x12!\n" +
	"\ffirebase_uid\x18\x01 \x01(\tR\vfirebaseUid\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\"\x0f\n" +
	"\rLoginResponse2\x9b\x01\n" +
	"\vUserService\x12H\n" +
	"\aGetUser\x12\x1d.proto.user.v1.GetUserRequest\x1a\x1e.proto.user.v1.GetUserResponse\x12B\n" +
	"\x05Login\x12\x1b.proto.user.v1.LoginRequest\x1a\x1c.proto.user.v1.LoginResponseB-Z+fastserver.com/fastserver/gen/proto/user/v1b\x06proto3"

var (
	file_proto_user_v1_service_proto_rawDescOnce sync.Once
	file_proto_user_v1_service_proto_rawDescData []byte
)

func file_proto_user_v1_service_proto_rawDescGZIP() []byte {
	file_proto_user_v1_service_proto_rawDescOnce.Do(func() {
		file_proto_user_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_user_v1_service_proto_rawDesc), len(file_proto_user_v1_service_proto_rawDesc)))
	})
	return file_proto_user_v1_service_proto_rawDescData
}

var file_proto_user_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_user_v1_service_proto_goTypes = []any{
	(*User)(nil),            // 0: proto.user.v1.User
	(*GetUserRequest)(nil),  // 1: proto.user.v1.GetUserRequest
	(*GetUserResponse)(nil), // 2: proto.user.v1.GetUserResponse
	(*LoginRequest)(nil),    // 3: proto.user.v1.LoginRequest
	(*LoginResponse)(nil),   // 4: proto.user.v1.LoginResponse
}
var file_proto_user_v1_service_proto_depIdxs = []int32{
	0, // 0: proto.user.v1.GetUserResponse.user:type_name -> proto.user.v1.User
	1, // 1: proto.user.v1.UserService.GetUser:input_type -> proto.user.v1.GetUserRequest
	3, // 2: proto.user.v1.UserService.Login:input_type -> proto.user.v1.LoginRequest
	2, // 3: proto.user.v1.UserService.GetUser:output_type -> proto.user.v1.GetUserResponse
	4, // 4: proto.user.v1.UserService.Login:output_type -> proto.user.v1.LoginResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_user_v1_service_proto_init() }
func file_proto_user_v1_service_proto_init() {
	if File_proto_user_v1_service_proto != nil {
		return
	}
	file_proto_user_v1_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_proto_user_v1_service_proto_msgTypes[1].OneofWrappers = []any{
		(*GetUserRequest_Id)(nil),
		(*GetUserRequest_FirebaseUid)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_user_v1_service_proto_rawDesc), len(file_proto_user_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_user_v1_service_proto_goTypes,
		DependencyIndexes: file_proto_user_v1_service_proto_depIdxs,
		MessageInfos:      file_proto_user_v1_service_proto_msgTypes,
	}.Build()
	File_proto_user_v1_service_proto = out.File
	file_proto_user_v1_service_proto_goTypes = nil
	file_proto_user_v1_service_proto_depIdxs = nil
}
