// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proto/stt/v1/service.proto

package v1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "fastserver.com/fastserver/gen/proto/stt/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// STTServiceName is the fully-qualified name of the STTService service.
	STTServiceName = "proto.stt.v1.STTService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// STTServiceTranscribeAudioProcedure is the fully-qualified name of the STTService's
	// TranscribeAudio RPC.
	STTServiceTranscribeAudioProcedure = "/proto.stt.v1.STTService/TranscribeAudio"
)

// STTServiceClient is a client for the proto.stt.v1.STTService service.
type STTServiceClient interface {
	// TranscribeAudio 将音频转换为文本
	TranscribeAudio(context.Context, *connect.Request[v1.TranscribeRequest]) (*connect.Response[v1.TranscribeResponse], error)
}

// NewSTTServiceClient constructs a client for the proto.stt.v1.STTService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewSTTServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) STTServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	sTTServiceMethods := v1.File_proto_stt_v1_service_proto.Services().ByName("STTService").Methods()
	return &sTTServiceClient{
		transcribeAudio: connect.NewClient[v1.TranscribeRequest, v1.TranscribeResponse](
			httpClient,
			baseURL+STTServiceTranscribeAudioProcedure,
			connect.WithSchema(sTTServiceMethods.ByName("TranscribeAudio")),
			connect.WithClientOptions(opts...),
		),
	}
}

// sTTServiceClient implements STTServiceClient.
type sTTServiceClient struct {
	transcribeAudio *connect.Client[v1.TranscribeRequest, v1.TranscribeResponse]
}

// TranscribeAudio calls proto.stt.v1.STTService.TranscribeAudio.
func (c *sTTServiceClient) TranscribeAudio(ctx context.Context, req *connect.Request[v1.TranscribeRequest]) (*connect.Response[v1.TranscribeResponse], error) {
	return c.transcribeAudio.CallUnary(ctx, req)
}

// STTServiceHandler is an implementation of the proto.stt.v1.STTService service.
type STTServiceHandler interface {
	// TranscribeAudio 将音频转换为文本
	TranscribeAudio(context.Context, *connect.Request[v1.TranscribeRequest]) (*connect.Response[v1.TranscribeResponse], error)
}

// NewSTTServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewSTTServiceHandler(svc STTServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	sTTServiceMethods := v1.File_proto_stt_v1_service_proto.Services().ByName("STTService").Methods()
	sTTServiceTranscribeAudioHandler := connect.NewUnaryHandler(
		STTServiceTranscribeAudioProcedure,
		svc.TranscribeAudio,
		connect.WithSchema(sTTServiceMethods.ByName("TranscribeAudio")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proto.stt.v1.STTService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case STTServiceTranscribeAudioProcedure:
			sTTServiceTranscribeAudioHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedSTTServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedSTTServiceHandler struct{}

func (UnimplementedSTTServiceHandler) TranscribeAudio(context.Context, *connect.Request[v1.TranscribeRequest]) (*connect.Response[v1.TranscribeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.stt.v1.STTService.TranscribeAudio is not implemented"))
}
