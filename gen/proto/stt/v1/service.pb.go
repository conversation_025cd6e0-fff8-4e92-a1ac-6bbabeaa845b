// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/stt/v1/service.proto

package v1

import (
	v1 "fastserver.com/fastserver/gen/proto/task/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AudioFormat 定义支持的音频格式
type AudioFormat int32

const (
	AudioFormat_AUDIO_FORMAT_UNSPECIFIED AudioFormat = 0
	AudioFormat_MP3                      AudioFormat = 1
	AudioFormat_WAV                      AudioFormat = 2
	AudioFormat_M4A                      AudioFormat = 3
	AudioFormat_OGG                      AudioFormat = 4
	AudioFormat_FLAC                     AudioFormat = 5
	AudioFormat_PCM                      AudioFormat = 6
)

// Enum value maps for AudioFormat.
var (
	AudioFormat_name = map[int32]string{
		0: "AUDIO_FORMAT_UNSPECIFIED",
		1: "MP3",
		2: "WAV",
		3: "M4A",
		4: "OGG",
		5: "FLAC",
		6: "PCM",
	}
	AudioFormat_value = map[string]int32{
		"AUDIO_FORMAT_UNSPECIFIED": 0,
		"MP3":                      1,
		"WAV":                      2,
		"M4A":                      3,
		"OGG":                      4,
		"FLAC":                     5,
		"PCM":                      6,
	}
)

func (x AudioFormat) Enum() *AudioFormat {
	p := new(AudioFormat)
	*p = x
	return p
}

func (x AudioFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AudioFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_stt_v1_service_proto_enumTypes[0].Descriptor()
}

func (AudioFormat) Type() protoreflect.EnumType {
	return &file_proto_stt_v1_service_proto_enumTypes[0]
}

func (x AudioFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AudioFormat.Descriptor instead.
func (AudioFormat) EnumDescriptor() ([]byte, []int) {
	return file_proto_stt_v1_service_proto_rawDescGZIP(), []int{0}
}

// TranscribeRequest 定义语音转文本的请求结构
type TranscribeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 音频数据，base64编码
	AudioData string `protobuf:"bytes,1,opt,name=audio_data,json=audioData,proto3" json:"audio_data,omitempty"`
	// 音频格式
	AudioFormat AudioFormat `protobuf:"varint,2,opt,name=audio_format,json=audioFormat,proto3,enum=proto.stt.v1.AudioFormat" json:"audio_format,omitempty"`
	// 可选：指定使用的模型
	Model string `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	// 可选：语言代码 (如 "zh-CN", "en-US")
	LanguageCode string `protobuf:"bytes,4,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"`
	// 可选：自定义提示词，用于指导转录结果的格式
	Prompt string `protobuf:"bytes,5,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// 可选：是否启用任务分析模式（返回结构化的任务信息）
	EnableTaskAnalysis bool `protobuf:"varint,6,opt,name=enable_task_analysis,json=enableTaskAnalysis,proto3" json:"enable_task_analysis,omitempty"`
	// 可选：用户已安装的应用列表，用于提升任务解析准确性
	InstalledApps []string `protobuf:"bytes,7,rep,name=installed_apps,json=installedApps,proto3" json:"installed_apps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscribeRequest) Reset() {
	*x = TranscribeRequest{}
	mi := &file_proto_stt_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscribeRequest) ProtoMessage() {}

func (x *TranscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_stt_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscribeRequest.ProtoReflect.Descriptor instead.
func (*TranscribeRequest) Descriptor() ([]byte, []int) {
	return file_proto_stt_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *TranscribeRequest) GetAudioData() string {
	if x != nil {
		return x.AudioData
	}
	return ""
}

func (x *TranscribeRequest) GetAudioFormat() AudioFormat {
	if x != nil {
		return x.AudioFormat
	}
	return AudioFormat_AUDIO_FORMAT_UNSPECIFIED
}

func (x *TranscribeRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *TranscribeRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *TranscribeRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *TranscribeRequest) GetEnableTaskAnalysis() bool {
	if x != nil {
		return x.EnableTaskAnalysis
	}
	return false
}

func (x *TranscribeRequest) GetInstalledApps() []string {
	if x != nil {
		return x.InstalledApps
	}
	return nil
}

// TranscribeResponse 定义语音转文本的响应结构
type TranscribeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 转录的文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 可选：任务分析结果（当 enable_task_analysis 为 true 时）
	TaskInfo *v1.SimpleTask `protobuf:"bytes,2,opt,name=task_info,json=taskInfo,proto3" json:"task_info,omitempty"`
	// 错误信息，如果转录失败则不为空
	Error string `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	// Token 使用情况
	TokenUsage *v1.TokenUsage `protobuf:"bytes,4,opt,name=token_usage,json=tokenUsage,proto3" json:"token_usage,omitempty"`
	// 转录完成时间
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	// 使用的模型名称
	ModelUsed string `protobuf:"bytes,6,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	// 音频时长（秒）
	AudioDuration float32 `protobuf:"fixed32,7,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscribeResponse) Reset() {
	*x = TranscribeResponse{}
	mi := &file_proto_stt_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscribeResponse) ProtoMessage() {}

func (x *TranscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_stt_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscribeResponse.ProtoReflect.Descriptor instead.
func (*TranscribeResponse) Descriptor() ([]byte, []int) {
	return file_proto_stt_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *TranscribeResponse) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TranscribeResponse) GetTaskInfo() *v1.SimpleTask {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *TranscribeResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *TranscribeResponse) GetTokenUsage() *v1.TokenUsage {
	if x != nil {
		return x.TokenUsage
	}
	return nil
}

func (x *TranscribeResponse) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *TranscribeResponse) GetModelUsed() string {
	if x != nil {
		return x.ModelUsed
	}
	return ""
}

func (x *TranscribeResponse) GetAudioDuration() float32 {
	if x != nil {
		return x.AudioDuration
	}
	return 0
}

var File_proto_stt_v1_service_proto protoreflect.FileDescriptor

const file_proto_stt_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x1aproto/stt/v1/service.proto\x12\fproto.stt.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bproto/task/v1/service.proto\"\x9c\x02\n" +
	"\x11TranscribeRequest\x12\x1d\n" +
	"\n" +
	"audio_data\x18\x01 \x01(\tR\taudioData\x12<\n" +
	"\faudio_format\x18\x02 \x01(\x0e2\x19.proto.stt.v1.AudioFormatR\vaudioFormat\x12\x14\n" +
	"\x05model\x18\x03 \x01(\tR\x05model\x12#\n" +
	"\rlanguage_code\x18\x04 \x01(\tR\flanguageCode\x12\x16\n" +
	"\x06prompt\x18\x05 \x01(\tR\x06prompt\x120\n" +
	"\x14enable_task_analysis\x18\x06 \x01(\bR\x12enableTaskAnalysis\x12%\n" +
	"\x0einstalled_apps\x18\a \x03(\tR\rinstalledApps\"\xb7\x02\n" +
	"\x12TranscribeResponse\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x126\n" +
	"\ttask_info\x18\x02 \x01(\v2\x19.proto.task.v1.SimpleTaskR\btaskInfo\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\x12:\n" +
	"\vtoken_usage\x18\x04 \x01(\v2\x19.proto.task.v1.TokenUsageR\n" +
	"tokenUsage\x12=\n" +
	"\fcompleted_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x12\x1d\n" +
	"\n" +
	"model_used\x18\x06 \x01(\tR\tmodelUsed\x12%\n" +
	"\x0eaudio_duration\x18\a \x01(\x02R\raudioDuration*b\n" +
	"\vAudioFormat\x12\x1c\n" +
	"\x18AUDIO_FORMAT_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03MP3\x10\x01\x12\a\n" +
	"\x03WAV\x10\x02\x12\a\n" +
	"\x03M4A\x10\x03\x12\a\n" +
	"\x03OGG\x10\x04\x12\b\n" +
	"\x04FLAC\x10\x05\x12\a\n" +
	"\x03PCM\x10\x062d\n" +
	"\n" +
	"STTService\x12V\n" +
	"\x0fTranscribeAudio\x12\x1f.proto.stt.v1.TranscribeRequest\x1a .proto.stt.v1.TranscribeResponse\"\x00B,Z*fastserver.com/fastserver/gen/proto/stt/v1b\x06proto3"

var (
	file_proto_stt_v1_service_proto_rawDescOnce sync.Once
	file_proto_stt_v1_service_proto_rawDescData []byte
)

func file_proto_stt_v1_service_proto_rawDescGZIP() []byte {
	file_proto_stt_v1_service_proto_rawDescOnce.Do(func() {
		file_proto_stt_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_stt_v1_service_proto_rawDesc), len(file_proto_stt_v1_service_proto_rawDesc)))
	})
	return file_proto_stt_v1_service_proto_rawDescData
}

var file_proto_stt_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_stt_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_stt_v1_service_proto_goTypes = []any{
	(AudioFormat)(0),              // 0: proto.stt.v1.AudioFormat
	(*TranscribeRequest)(nil),     // 1: proto.stt.v1.TranscribeRequest
	(*TranscribeResponse)(nil),    // 2: proto.stt.v1.TranscribeResponse
	(*v1.SimpleTask)(nil),         // 3: proto.task.v1.SimpleTask
	(*v1.TokenUsage)(nil),         // 4: proto.task.v1.TokenUsage
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_proto_stt_v1_service_proto_depIdxs = []int32{
	0, // 0: proto.stt.v1.TranscribeRequest.audio_format:type_name -> proto.stt.v1.AudioFormat
	3, // 1: proto.stt.v1.TranscribeResponse.task_info:type_name -> proto.task.v1.SimpleTask
	4, // 2: proto.stt.v1.TranscribeResponse.token_usage:type_name -> proto.task.v1.TokenUsage
	5, // 3: proto.stt.v1.TranscribeResponse.completed_at:type_name -> google.protobuf.Timestamp
	1, // 4: proto.stt.v1.STTService.TranscribeAudio:input_type -> proto.stt.v1.TranscribeRequest
	2, // 5: proto.stt.v1.STTService.TranscribeAudio:output_type -> proto.stt.v1.TranscribeResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_stt_v1_service_proto_init() }
func file_proto_stt_v1_service_proto_init() {
	if File_proto_stt_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_stt_v1_service_proto_rawDesc), len(file_proto_stt_v1_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_stt_v1_service_proto_goTypes,
		DependencyIndexes: file_proto_stt_v1_service_proto_depIdxs,
		EnumInfos:         file_proto_stt_v1_service_proto_enumTypes,
		MessageInfos:      file_proto_stt_v1_service_proto_msgTypes,
	}.Build()
	File_proto_stt_v1_service_proto = out.File
	file_proto_stt_v1_service_proto_goTypes = nil
	file_proto_stt_v1_service_proto_depIdxs = nil
}
