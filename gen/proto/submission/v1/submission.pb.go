// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proto/submission/v1/submission.proto

package submissionv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents a submitted deeplink record (mirrors store.SubmittedDeeplink)
type SubmittedDeeplink struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	Id                int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId            string                  `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // UUID as string
	Deeplink          *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Name              *wrapperspb.StringValue `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type              *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	RequiresTextInput *wrapperspb.BoolValue   `protobuf:"bytes,6,opt,name=requires_text_input,json=requiresTextInput,proto3" json:"requires_text_input,omitempty"`
	OpenWithApp       *wrapperspb.StringValue `protobuf:"bytes,7,opt,name=open_with_app,json=openWithApp,proto3" json:"open_with_app,omitempty"`
	OpenWithActivity  *wrapperspb.StringValue `protobuf:"bytes,8,opt,name=open_with_activity,json=openWithActivity,proto3" json:"open_with_activity,omitempty"`
	Status            *wrapperspb.StringValue `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	AppPackageName    *wrapperspb.StringValue `protobuf:"bytes,10,opt,name=app_package_name,json=appPackageName,proto3" json:"app_package_name,omitempty"`
	CreatedAt         *timestamppb.Timestamp  `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp  `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SubmittedDeeplink) Reset() {
	*x = SubmittedDeeplink{}
	mi := &file_proto_submission_v1_submission_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmittedDeeplink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmittedDeeplink) ProtoMessage() {}

func (x *SubmittedDeeplink) ProtoReflect() protoreflect.Message {
	mi := &file_proto_submission_v1_submission_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmittedDeeplink.ProtoReflect.Descriptor instead.
func (*SubmittedDeeplink) Descriptor() ([]byte, []int) {
	return file_proto_submission_v1_submission_proto_rawDescGZIP(), []int{0}
}

func (x *SubmittedDeeplink) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubmittedDeeplink) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SubmittedDeeplink) GetDeeplink() *wrapperspb.StringValue {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *SubmittedDeeplink) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *SubmittedDeeplink) GetType() *wrapperspb.StringValue {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *SubmittedDeeplink) GetRequiresTextInput() *wrapperspb.BoolValue {
	if x != nil {
		return x.RequiresTextInput
	}
	return nil
}

func (x *SubmittedDeeplink) GetOpenWithApp() *wrapperspb.StringValue {
	if x != nil {
		return x.OpenWithApp
	}
	return nil
}

func (x *SubmittedDeeplink) GetOpenWithActivity() *wrapperspb.StringValue {
	if x != nil {
		return x.OpenWithActivity
	}
	return nil
}

func (x *SubmittedDeeplink) GetStatus() *wrapperspb.StringValue {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SubmittedDeeplink) GetAppPackageName() *wrapperspb.StringValue {
	if x != nil {
		return x.AppPackageName
	}
	return nil
}

func (x *SubmittedDeeplink) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SubmittedDeeplink) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request message for submitting a deeplink
type SubmitDeeplinkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required: The deeplink URL itself.
	Deeplink string `protobuf:"bytes,1,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Required: A user-friendly name for the deeplink.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Required: The type of the deeplink (e.g., 'activity', 'service', 'broadcast').
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// Required: Package name of the target app.
	AppPackageName string `protobuf:"bytes,4,opt,name=app_package_name,json=appPackageName,proto3" json:"app_package_name,omitempty"`
	// Optional: Does this deeplink require text input from the user?
	RequiresTextInput *bool `protobuf:"varint,5,opt,name=requires_text_input,json=requiresTextInput,proto3,oneof" json:"requires_text_input,omitempty"`
	// Optional: Specific app to open with (if different from app_package_name, e.g., chooser).
	OpenWithApp *string `protobuf:"bytes,6,opt,name=open_with_app,json=openWithApp,proto3,oneof" json:"open_with_app,omitempty"`
	// Optional: Specific activity component to target within the app.
	OpenWithActivity *string `protobuf:"bytes,7,opt,name=open_with_activity,json=openWithActivity,proto3,oneof" json:"open_with_activity,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SubmitDeeplinkRequest) Reset() {
	*x = SubmitDeeplinkRequest{}
	mi := &file_proto_submission_v1_submission_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitDeeplinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitDeeplinkRequest) ProtoMessage() {}

func (x *SubmitDeeplinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_submission_v1_submission_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitDeeplinkRequest.ProtoReflect.Descriptor instead.
func (*SubmitDeeplinkRequest) Descriptor() ([]byte, []int) {
	return file_proto_submission_v1_submission_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitDeeplinkRequest) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *SubmitDeeplinkRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubmitDeeplinkRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SubmitDeeplinkRequest) GetAppPackageName() string {
	if x != nil {
		return x.AppPackageName
	}
	return ""
}

func (x *SubmitDeeplinkRequest) GetRequiresTextInput() bool {
	if x != nil && x.RequiresTextInput != nil {
		return *x.RequiresTextInput
	}
	return false
}

func (x *SubmitDeeplinkRequest) GetOpenWithApp() string {
	if x != nil && x.OpenWithApp != nil {
		return *x.OpenWithApp
	}
	return ""
}

func (x *SubmitDeeplinkRequest) GetOpenWithActivity() string {
	if x != nil && x.OpenWithActivity != nil {
		return *x.OpenWithActivity
	}
	return ""
}

// Response message after submitting a deeplink
type SubmitDeeplinkResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The details of the submitted deeplink record created.
	SubmittedDeeplink *SubmittedDeeplink `protobuf:"bytes,1,opt,name=submitted_deeplink,json=submittedDeeplink,proto3" json:"submitted_deeplink,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SubmitDeeplinkResponse) Reset() {
	*x = SubmitDeeplinkResponse{}
	mi := &file_proto_submission_v1_submission_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitDeeplinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitDeeplinkResponse) ProtoMessage() {}

func (x *SubmitDeeplinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_submission_v1_submission_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitDeeplinkResponse.ProtoReflect.Descriptor instead.
func (*SubmitDeeplinkResponse) Descriptor() ([]byte, []int) {
	return file_proto_submission_v1_submission_proto_rawDescGZIP(), []int{2}
}

func (x *SubmitDeeplinkResponse) GetSubmittedDeeplink() *SubmittedDeeplink {
	if x != nil {
		return x.SubmittedDeeplink
	}
	return nil
}

var File_proto_submission_v1_submission_proto protoreflect.FileDescriptor

const file_proto_submission_v1_submission_proto_rawDesc = "" +
	"\n" +
	"$proto/submission/v1/submission.proto\x12\x13proto.submission.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto\"\xa8\x05\n" +
	"\x11SubmittedDeeplink\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x128\n" +
	"\bdeeplink\x18\x03 \x01(\v2\x1c.google.protobuf.StringValueR\bdeeplink\x120\n" +
	"\x04name\x18\x04 \x01(\v2\x1c.google.protobuf.StringValueR\x04name\x120\n" +
	"\x04type\x18\x05 \x01(\v2\x1c.google.protobuf.StringValueR\x04type\x12J\n" +
	"\x13requires_text_input\x18\x06 \x01(\v2\x1a.google.protobuf.BoolValueR\x11requiresTextInput\x12@\n" +
	"\ropen_with_app\x18\a \x01(\v2\x1c.google.protobuf.StringValueR\vopenWithApp\x12J\n" +
	"\x12open_with_activity\x18\b \x01(\v2\x1c.google.protobuf.StringValueR\x10openWithActivity\x124\n" +
	"\x06status\x18\t \x01(\v2\x1c.google.protobuf.StringValueR\x06status\x12F\n" +
	"\x10app_package_name\x18\n" +
	" \x01(\v2\x1c.google.protobuf.StringValueR\x0eappPackageName\x129\n" +
	"\n" +
	"created_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xd7\x02\n" +
	"\x15SubmitDeeplinkRequest\x12\x1a\n" +
	"\bdeeplink\x18\x01 \x01(\tR\bdeeplink\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12(\n" +
	"\x10app_package_name\x18\x04 \x01(\tR\x0eappPackageName\x123\n" +
	"\x13requires_text_input\x18\x05 \x01(\bH\x00R\x11requiresTextInput\x88\x01\x01\x12'\n" +
	"\ropen_with_app\x18\x06 \x01(\tH\x01R\vopenWithApp\x88\x01\x01\x121\n" +
	"\x12open_with_activity\x18\a \x01(\tH\x02R\x10openWithActivity\x88\x01\x01B\x16\n" +
	"\x14_requires_text_inputB\x10\n" +
	"\x0e_open_with_appB\x15\n" +
	"\x13_open_with_activity\"o\n" +
	"\x16SubmitDeeplinkResponse\x12U\n" +
	"\x12submitted_deeplink\x18\x01 \x01(\v2&.proto.submission.v1.SubmittedDeeplinkR\x11submittedDeeplink2~\n" +
	"\x11SubmissionService\x12i\n" +
	"\x0eSubmitDeeplink\x12*.proto.submission.v1.SubmitDeeplinkRequest\x1a+.proto.submission.v1.SubmitDeeplinkResponseB@Z>fastserver.com/fastserver/gen/proto/submission/v1;submissionv1b\x06proto3"

var (
	file_proto_submission_v1_submission_proto_rawDescOnce sync.Once
	file_proto_submission_v1_submission_proto_rawDescData []byte
)

func file_proto_submission_v1_submission_proto_rawDescGZIP() []byte {
	file_proto_submission_v1_submission_proto_rawDescOnce.Do(func() {
		file_proto_submission_v1_submission_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_submission_v1_submission_proto_rawDesc), len(file_proto_submission_v1_submission_proto_rawDesc)))
	})
	return file_proto_submission_v1_submission_proto_rawDescData
}

var file_proto_submission_v1_submission_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_submission_v1_submission_proto_goTypes = []any{
	(*SubmittedDeeplink)(nil),      // 0: proto.submission.v1.SubmittedDeeplink
	(*SubmitDeeplinkRequest)(nil),  // 1: proto.submission.v1.SubmitDeeplinkRequest
	(*SubmitDeeplinkResponse)(nil), // 2: proto.submission.v1.SubmitDeeplinkResponse
	(*wrapperspb.StringValue)(nil), // 3: google.protobuf.StringValue
	(*wrapperspb.BoolValue)(nil),   // 4: google.protobuf.BoolValue
	(*timestamppb.Timestamp)(nil),  // 5: google.protobuf.Timestamp
}
var file_proto_submission_v1_submission_proto_depIdxs = []int32{
	3,  // 0: proto.submission.v1.SubmittedDeeplink.deeplink:type_name -> google.protobuf.StringValue
	3,  // 1: proto.submission.v1.SubmittedDeeplink.name:type_name -> google.protobuf.StringValue
	3,  // 2: proto.submission.v1.SubmittedDeeplink.type:type_name -> google.protobuf.StringValue
	4,  // 3: proto.submission.v1.SubmittedDeeplink.requires_text_input:type_name -> google.protobuf.BoolValue
	3,  // 4: proto.submission.v1.SubmittedDeeplink.open_with_app:type_name -> google.protobuf.StringValue
	3,  // 5: proto.submission.v1.SubmittedDeeplink.open_with_activity:type_name -> google.protobuf.StringValue
	3,  // 6: proto.submission.v1.SubmittedDeeplink.status:type_name -> google.protobuf.StringValue
	3,  // 7: proto.submission.v1.SubmittedDeeplink.app_package_name:type_name -> google.protobuf.StringValue
	5,  // 8: proto.submission.v1.SubmittedDeeplink.created_at:type_name -> google.protobuf.Timestamp
	5,  // 9: proto.submission.v1.SubmittedDeeplink.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 10: proto.submission.v1.SubmitDeeplinkResponse.submitted_deeplink:type_name -> proto.submission.v1.SubmittedDeeplink
	1,  // 11: proto.submission.v1.SubmissionService.SubmitDeeplink:input_type -> proto.submission.v1.SubmitDeeplinkRequest
	2,  // 12: proto.submission.v1.SubmissionService.SubmitDeeplink:output_type -> proto.submission.v1.SubmitDeeplinkResponse
	12, // [12:13] is the sub-list for method output_type
	11, // [11:12] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_proto_submission_v1_submission_proto_init() }
func file_proto_submission_v1_submission_proto_init() {
	if File_proto_submission_v1_submission_proto != nil {
		return
	}
	file_proto_submission_v1_submission_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_submission_v1_submission_proto_rawDesc), len(file_proto_submission_v1_submission_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_submission_v1_submission_proto_goTypes,
		DependencyIndexes: file_proto_submission_v1_submission_proto_depIdxs,
		MessageInfos:      file_proto_submission_v1_submission_proto_msgTypes,
	}.Build()
	File_proto_submission_v1_submission_proto = out.File
	file_proto_submission_v1_submission_proto_goTypes = nil
	file_proto_submission_v1_submission_proto_depIdxs = nil
}
