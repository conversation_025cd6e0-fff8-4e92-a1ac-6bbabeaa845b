// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proto/submission/v1/submission.proto

package submissionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "fastserver.com/fastserver/gen/proto/submission/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// SubmissionServiceName is the fully-qualified name of the SubmissionService service.
	SubmissionServiceName = "proto.submission.v1.SubmissionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// SubmissionServiceSubmitDeeplinkProcedure is the fully-qualified name of the SubmissionService's
	// SubmitDeeplink RPC.
	SubmissionServiceSubmitDeeplinkProcedure = "/proto.submission.v1.SubmissionService/SubmitDeeplink"
)

// SubmissionServiceClient is a client for the proto.submission.v1.SubmissionService service.
type SubmissionServiceClient interface {
	// Submits a new deeplink for review.
	SubmitDeeplink(context.Context, *connect.Request[v1.SubmitDeeplinkRequest]) (*connect.Response[v1.SubmitDeeplinkResponse], error)
}

// NewSubmissionServiceClient constructs a client for the proto.submission.v1.SubmissionService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewSubmissionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) SubmissionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	submissionServiceMethods := v1.File_proto_submission_v1_submission_proto.Services().ByName("SubmissionService").Methods()
	return &submissionServiceClient{
		submitDeeplink: connect.NewClient[v1.SubmitDeeplinkRequest, v1.SubmitDeeplinkResponse](
			httpClient,
			baseURL+SubmissionServiceSubmitDeeplinkProcedure,
			connect.WithSchema(submissionServiceMethods.ByName("SubmitDeeplink")),
			connect.WithClientOptions(opts...),
		),
	}
}

// submissionServiceClient implements SubmissionServiceClient.
type submissionServiceClient struct {
	submitDeeplink *connect.Client[v1.SubmitDeeplinkRequest, v1.SubmitDeeplinkResponse]
}

// SubmitDeeplink calls proto.submission.v1.SubmissionService.SubmitDeeplink.
func (c *submissionServiceClient) SubmitDeeplink(ctx context.Context, req *connect.Request[v1.SubmitDeeplinkRequest]) (*connect.Response[v1.SubmitDeeplinkResponse], error) {
	return c.submitDeeplink.CallUnary(ctx, req)
}

// SubmissionServiceHandler is an implementation of the proto.submission.v1.SubmissionService
// service.
type SubmissionServiceHandler interface {
	// Submits a new deeplink for review.
	SubmitDeeplink(context.Context, *connect.Request[v1.SubmitDeeplinkRequest]) (*connect.Response[v1.SubmitDeeplinkResponse], error)
}

// NewSubmissionServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewSubmissionServiceHandler(svc SubmissionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	submissionServiceMethods := v1.File_proto_submission_v1_submission_proto.Services().ByName("SubmissionService").Methods()
	submissionServiceSubmitDeeplinkHandler := connect.NewUnaryHandler(
		SubmissionServiceSubmitDeeplinkProcedure,
		svc.SubmitDeeplink,
		connect.WithSchema(submissionServiceMethods.ByName("SubmitDeeplink")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proto.submission.v1.SubmissionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case SubmissionServiceSubmitDeeplinkProcedure:
			submissionServiceSubmitDeeplinkHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedSubmissionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedSubmissionServiceHandler struct{}

func (UnimplementedSubmissionServiceHandler) SubmitDeeplink(context.Context, *connect.Request[v1.SubmitDeeplinkRequest]) (*connect.Response[v1.SubmitDeeplinkResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proto.submission.v1.SubmissionService.SubmitDeeplink is not implemented"))
}
