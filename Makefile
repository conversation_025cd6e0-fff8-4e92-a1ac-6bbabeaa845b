# 定义命令执行函数
define execute_command
	@$(1) || (echo "错误: $(2)" && exit 1)
endef

# Binary names and paths
BINARY_NAME=fastserver
SERVER_BINARY_NAME=fastserver_linux_amd64

# Google Cloud Platform configuration
GCP_REMOTE_USER=fengyangsen
GCP_REMOTE_HOST=gcp-saien
GCP_REMOTE_PATH=/opt/fast
GCP_DEPLOY_CMD=ssh -n $(GCP_REMOTE_HOST)
GCP_TRANSFER_CMD=scp $(PWD)/$(SERVER_BINARY_NAME) $(GCP_REMOTE_HOST):$(GCP_REMOTE_PATH)
GCP_SUDO=sudo

define check_health
	@echo "4. Verifying service health..."
	sleep 5
	$(call execute_command,$(1) "if $(2) pgrep -f '$(SERVER_BINARY_NAME)' > /dev/null; then echo 'Service is running'; else echo 'Service start failed!'; exit 1; fi",Health check failed)
	@echo ">>> Service verified running"
endef

# 进程检查函数
define check_process
	@echo "1. Checking remote processes..."
	$(call execute_command,$(1) "ps aux | grep $(SERVER_BINARY_NAME) | grep -v grep",Process check failed)
	@echo "2. Attempting to terminate any existing processes..."
	-$(1) "$(2) pkill -f $(SERVER_BINARY_NAME) || true"
	@sleep 2
	-$(1) "$(2) pkill -9 -f $(SERVER_BINARY_NAME) || true"
	@echo ">>> Process check completed"
endef

# 部署函数
define deploy_gcp
	@echo "=== Starting GCP deployment process ==="
	$(call check_process,$(GCP_DEPLOY_CMD),$(GCP_SUDO))
	@echo "3. Transferring binary..."
	$(call execute_command,$(GCP_TRANSFER_CMD),Transfer failed)
	
	@echo "4. Setting executable permissions..."
	$(call execute_command,$(GCP_DEPLOY_CMD) "$(GCP_SUDO) chmod +x $(GCP_REMOTE_PATH)/$(SERVER_BINARY_NAME)",Permission setting failed)

	@echo "5. Starting remote service..."
	@ssh -f $(GCP_REMOTE_HOST) "cd $(GCP_REMOTE_PATH) && $(GCP_SUDO) nohup ./$(SERVER_BINARY_NAME) > fast_server.log 2>&1 & echo 'Service start command executed'"
	@echo ">>> Service started"
	
	@echo "6. Verifying service health (optional)..."
	@sleep 5
	-@$(GCP_DEPLOY_CMD) "ps aux | grep $(SERVER_BINARY_NAME) | grep -v grep" > /dev/null && echo ">>> Service is running" || echo "WARNING: Service may not be running, please check manually"
	
	@echo "=== Deployment complete ==="
endef

# Main deployment target
.PHONY: deploy tag_release deploy_and_tag
deploy: check_vars build_linux_amd64
	$(call deploy_gcp)
	@echo ">>> Deployment steps finished."

# Version tagging
tag_release:
	# Check for uncommitted changes before tagging
	@echo ">>> Checking for uncommitted changes before tagging..."
	@git diff --quiet && git diff --staged --quiet || (echo "ERROR: Uncommitted changes found. Cannot tag. Please commit or stash."; exit 1)
	# Generate tag name and create local tag
	@echo ">>> Generating and creating git tag..."
	$(eval TAG_NAME := v$(shell date +'%Y%m%d_%H%M'))
	@git tag $(TAG_NAME) || (echo "ERROR: Failed to create local tag $(TAG_NAME)"; exit 1)
	# Push the tag to the remote repository
	@echo ">>> Pushing tag $(TAG_NAME) to origin..."
	@git push origin $(TAG_NAME) || (echo "ERROR: Failed to push tag $(TAG_NAME) to origin"; exit 1)
	@echo ">>> Successfully tagged release as $(TAG_NAME) and pushed to origin."

# This target first deploys, then tags if deployment was successful
deploy_and_tag: deploy tag_release
	@echo ">>> Deployment and tagging complete."

# Build targets
.PHONY: build build_linux_amd64
build:
	@echo "Building for current platform with embedded configs..."
	@CGO_ENABLED=0 go build -ldflags '-w -s' -o $(BINARY_NAME) && echo "Build successful" || echo "Build failed"

build_linux_amd64:
	@echo "Building Linux/amd64 version with embedded configs..."
	@rm -f $(SERVER_BINARY_NAME)
	@GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -ldflags '-w -s' -o $(SERVER_BINARY_NAME) && \
	file $(SERVER_BINARY_NAME) && \
	echo "Build successful" || (echo "Build failed" && exit 1)

# Utility targets
.PHONY: clean run check_vars
clean:
	@echo "Cleaning up..."
	@rm -f $(BINARY_NAME) $(SERVER_BINARY_NAME) && echo "Cleanup successful" || echo "Cleanup failed"

run:
	@echo "Starting local service..."
	@go run . && echo "Service started"

check_vars:
	@[ -n "$(SERVER_BINARY_NAME)" ] || (echo "ERROR: SERVER_BINARY_NAME not set"; exit 1)