# 🚀 ADC配置快速指南

## ⚠️ 重要提醒

**这些脚本不能直接执行！** 需要先进行环境检查和配置。

## 📋 正确的执行步骤

### 步骤1: 环境预检查

```bash
# 给脚本执行权限
chmod +x scripts/pre-check.sh

# 运行预检查（这会检查您的环境并生成配置）
./scripts/pre-check.sh
```

**预检查会验证：**
- ✅ gcloud CLI是否安装
- ✅ 是否已登录gcloud
- ✅ 项目配置是否正确
- ✅ Speech-to-Text API是否启用
- ✅ IAM权限是否足够

### 步骤2: 加载配置

```bash
# 加载预检查生成的配置
source /tmp/adc-config.env

# 验证配置已加载
echo "项目ID: $PROJECT_ID"
echo "服务账号: $SERVICE_ACCOUNT_EMAIL"
```

### 步骤3: 运行ADC配置

```bash
# 给脚本执行权限
chmod +x scripts/setup-production-adc.sh

# 运行ADC配置脚本
./scripts/setup-production-adc.sh
```

### 步骤4: 验证配置

```bash
# 给脚本执行权限
chmod +x scripts/verify-adc.sh

# 运行验证脚本
./scripts/verify-adc.sh
```

## 🔧 当前环境状态

根据检查，您的环境状态：

- ✅ **gcloud CLI**: 已安装 (`/Users/<USER>/GreenSoft/google-cloud-sdk/bin/gcloud`)
- ✅ **认证状态**: 已登录 (`<EMAIL>`)
- ⚠️ **项目配置**: 当前项目是 `fast-4bb23`，但脚本配置的是 `helical-sol-417708`

## 🚨 需要解决的问题

### 1. 项目ID不匹配

您当前的gcloud项目是 `fast-4bb23`，但配置脚本中写的是 `helical-sol-417708`。

**解决方案A: 使用当前项目**
```bash
# 设置环境变量使用当前项目
export PROJECT_ID="fast-4bb23"
```

**解决方案B: 切换到配置的项目**
```bash
# 切换到脚本配置的项目
gcloud config set project helical-sol-417708
```

### 2. 权限检查

确保您的账号在目标项目中有以下权限：
- `roles/iam.serviceAccountAdmin` (创建服务账号)
- `roles/resourcemanager.projectIamAdmin` (分配IAM权限)
- 或者 `roles/owner` (项目所有者)

## 🎯 推荐执行流程

```bash
# 1. 预检查环境
./scripts/pre-check.sh

# 2. 根据预检查结果选择项目ID
# 如果要使用当前项目 fast-4bb23:
export PROJECT_ID="fast-4bb23"

# 3. 加载配置
source /tmp/adc-config.env

# 4. 运行配置脚本
./scripts/setup-production-adc.sh

# 5. 验证配置
./scripts/verify-adc.sh
```

## 🔍 故障排除

### 如果遇到权限错误：
```bash
# 检查当前用户权限
gcloud projects get-iam-policy $PROJECT_ID \
  --flatten="bindings[].members" \
  --filter="bindings.members:user:$(gcloud config get-value account)"
```

### 如果遇到API未启用：
```bash
# 启用必要的API
gcloud services enable speech.googleapis.com --project=$PROJECT_ID
gcloud services enable iam.googleapis.com --project=$PROJECT_ID
```

### 如果遇到项目访问问题：
```bash
# 检查项目是否存在且可访问
gcloud projects describe $PROJECT_ID
```

## 📞 获取帮助

如果遇到问题，请提供以下信息：
1. 预检查脚本的输出
2. 错误信息的完整内容
3. 您的gcloud配置：`gcloud config list`

## 🎉 成功标志

配置成功后，您应该看到：
- ✅ 服务账号创建成功
- ✅ IAM权限分配成功
- ✅ 凭据文件生成成功
- ✅ ADC测试通过
- ✅ Speech-to-Text API调用成功

然后就可以部署应用程序了！
