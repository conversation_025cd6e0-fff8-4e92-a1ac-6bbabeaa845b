# Webhook过滤功能使用示例

## 测试场景

假设数据库中有以下插件数据：

### 插件A - 混合类型插件
- App名称: "测试应用A"
- Deeplinks:
  - Deeplink 1: type="deeplink", name="打开主页"
  - Deeplink 2: type="webhook", name="发送通知"
  - Deeplink 3: type="action_send", name="分享内容"

### 插件B - 纯Webhook插件
- App名称: "测试应用B"  
- Deeplinks:
  - Deeplink 1: type="webhook", name="API调用"
  - Deeplink 2: type="webhook", name="数据同步"

## API调用示例

### 场景1: 旧版本客户端 (app-version: 2.3.0)

**请求:**
```http
GET /proto.store.v1.StoreService/LoadMorePlugins
Headers:
  app-version: 2.3.0
  Content-Type: application/json
```

**响应结果:**
- 插件A: 只包含2个deeplinks (过滤掉webhook类型)
  - Deeplink 1: type="deeplink", name="打开主页"
  - Deeplink 3: type="action_send", name="分享内容"
- 插件B: 完全被过滤掉 (所有deeplinks都是webhook类型)

**日志输出:**
```
INFO: Received LoadMorePlugins request: ... app version=2.3.0
INFO: Filtered webhook plugins for app version 2.3.0
```

### 场景2: 新版本客户端 (app-version: 2.4.0)

**请求:**
```http
GET /proto.store.v1.StoreService/LoadMorePlugins
Headers:
  app-version: 2.4.0
  Content-Type: application/json
```

**响应结果:**
- 插件A: 包含所有3个deeplinks (不过滤)
  - Deeplink 1: type="deeplink", name="打开主页"
  - Deeplink 2: type="webhook", name="发送通知"
  - Deeplink 3: type="action_send", name="分享内容"
- 插件B: 包含所有2个deeplinks (不过滤)
  - Deeplink 1: type="webhook", name="API调用"
  - Deeplink 2: type="webhook", name="数据同步"

**日志输出:**
```
INFO: Received LoadMorePlugins request: ... app version=2.4.0
```

### 场景3: 无版本信息的客户端

**请求:**
```http
GET /proto.store.v1.StoreService/LoadMorePlugins
Headers:
  Content-Type: application/json
```

**响应结果:**
- 插件A: 只包含2个deeplinks (过滤掉webhook类型)
- 插件B: 完全被过滤掉

**日志输出:**
```
INFO: Received LoadMorePlugins request: ... app version=
INFO: Filtered webhook plugins for app version 
```

## 测试命令

### 使用curl测试

```bash
# 测试旧版本客户端
curl -X POST "http://localhost:8080/proto.store.v1.StoreService/LoadMorePlugins" \
  -H "app-version: 2.3.0" \
  -H "Content-Type: application/json" \
  -d '{"limit": 10, "offset": 0}'

# 测试新版本客户端  
curl -X POST "http://localhost:8080/proto.store.v1.StoreService/LoadMorePlugins" \
  -H "app-version: 2.4.0" \
  -H "Content-Type: application/json" \
  -d '{"limit": 10, "offset": 0}'

# 测试无版本信息
curl -X POST "http://localhost:8080/proto.store.v1.StoreService/LoadMorePlugins" \
  -H "Content-Type: application/json" \
  -d '{"limit": 10, "offset": 0}'
```

### 验证过滤效果

1. 比较不同版本请求的响应结果
2. 检查服务器日志中的过滤信息
3. 确认旧版本客户端不会收到webhook类型的插件

## 注意事项

1. 过滤是在服务端透明进行的，客户端无需特殊处理
2. 建议客户端始终发送正确的版本信息
3. 新版本客户端可以正常处理所有类型的插件
4. 过滤逻辑确保向后兼容性，不会破坏现有功能 