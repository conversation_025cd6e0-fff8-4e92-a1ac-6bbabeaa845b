# Webhook插件过滤功能

## 概述

为了保证向后兼容性，当用户的app版本 <= 2.3.0 时，系统会自动过滤掉webhook类型的插件，避免旧版本客户端接收到不支持的插件类型。

## 实现细节

### 版本检查逻辑

- 从HTTP请求头中获取 `app-version` 或 `App-Version` 字段
- 解析版本号（支持 `v2.3.0` 和 `2.3.0` 格式）
- 与目标版本 `2.3.0` 进行比较
- 如果版本 <= 2.3.0 或版本信息无效/缺失，则过滤webhook插件

### 过滤策略

1. **插件级别过滤**: 如果一个插件的所有deeplinks都是webhook类型，则整个插件被过滤掉
2. **Deeplink级别过滤**: 如果插件包含多种类型的deeplinks，只过滤掉webhook类型的deeplinks，保留其他类型
3. **安全优先**: 当版本信息缺失或无效时，默认进行过滤

### 影响的API接口

以下接口会应用webhook过滤逻辑：

- `GetPlugin` - 获取单个插件详情
- `GetPresetPlugins` - 获取预设插件列表  
- `LoadMorePlugins` - 加载更多插件

### 请求头要求

客户端需要在请求头中包含版本信息：

```
app-version: 2.4.0
```

或

```
App-Version: 2.4.0
```

### 版本比较示例

| App版本 | 是否过滤Webhook | 说明 |
|---------|----------------|------|
| 2.2.9   | ✅ 是          | 小于2.3.0 |
| 2.3.0   | ✅ 是          | 等于2.3.0 |
| 2.3.1   | ❌ 否          | 大于2.3.0 |
| 2.4.0   | ❌ 否          | 大于2.3.0 |
| 3.0.0   | ❌ 否          | 大于2.3.0 |
| (空)    | ✅ 是          | 安全策略 |
| invalid | ✅ 是          | 安全策略 |

## 日志记录

当进行webhook过滤时，系统会记录相应的日志信息：

```
INFO: Filtered webhook plugins for app version 2.3.0
```

## 测试

可以通过以下命令运行相关测试：

```bash
go test ./biz/store -v -run TestShouldFilterWebhook
go test ./biz/store -v -run TestVersion
```

## 注意事项

1. 此功能仅影响v1版本的API接口
2. webhook类型的判断基于deeplink的 `type` 字段值为 `"webhook"`
3. 过滤是在服务端进行的，客户端无需额外处理
4. 功能向前兼容，新版本客户端可以正常接收webhook插件 