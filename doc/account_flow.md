```mermaid
sequenceDiagram
    title 获取订阅状态流程 (优化后)
    participant Client
    participant Firebase_Client as Firebase Client
    participant Server
    participant Firebase_Server as Firebase Server

    Client->>Firebase_Client: getUserInfo()
    Firebase_Client-->>Client: UserInfo (成功) / null (失败)
    alt UserInfo 为 null (未登录)
        Client-->>Client:  提示用户登录
    else UserInfo 存在 (已登录)
        Client->>Firebase_Client: getIdToken()
        Firebase_Client-->>Client: Firebase ID Token (成功) / 错误
        alt 获取 Token 失败
            Client-->>Client:  提示用户重试或检查网络
        else 获取 Token 成功
            Client->>Server: getSubStatus(Firebase ID Token)
            Server->>Firebase_Server: verifyIdToken(Firebase ID Token)
            Firebase_Server-->>Server: Login Status (Valid) / (Invalid)
            alt Login Status 为 Invalid (Token 验证失败)
                Server-->>Client: SubStatus Error (身份验证失败)
                Client-->>Client:  提示用户重新登录
            else Login Status 为 Valid (Token 验证成功)
                Server->>Server: 查询用户订阅状态 (Google Play API 或数据库)
                Server-->>Server: SubStatus (Active, Inactive, Error)
                alt SubStatus 为 Error (查询订阅状态失败)
                    Server-->>Client: SubStatus Error (服务器错误)
                    Client-->>Client:  提示用户稍后重试
                else SubStatus 为 Active 或 Inactive
                    Server-->>Client: SubStatus (Active / Inactive)
                    Client-->>Client: 根据 SubStatus 控制功能访问
                end
            end
        end
    end

```

