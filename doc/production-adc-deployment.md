# 生产环境ADC部署指南

## 🎯 概述

本指南将帮助您在生产服务器上配置Application Default Credentials (ADC)，实现安全、可靠的Google Cloud Speech-to-Text认证。

## 🏗️ 架构说明

```
生产服务器
├── /opt/fast-server/
│   ├── fastserver (应用程序)
│   ├── config/config.yaml
│   └── credentials/
│       └── service-account.json (ADC凭据)
├── /etc/systemd/system/
│   └── fast-server.service (系统服务)
└── 环境变量: GOOGLE_APPLICATION_CREDENTIALS
```

## 📋 部署步骤

### 步骤1: 准备工作

```bash
# 在本地机器上执行
cd /Users/<USER>/projects/fast-server

# 确保已登录gcloud
gcloud auth login
gcloud config set project helical-sol-417708
```

### 步骤2: 运行ADC配置脚本

```bash
# 运行自动化配置脚本
chmod +x scripts/setup-production-adc.sh
./scripts/setup-production-adc.sh

# 或者指定自定义服务账号名称
./scripts/setup-production-adc.sh my-custom-stt-service
```

### 步骤3: 部署应用程序

```bash
# 编译应用程序
make build

# 将应用程序传输到服务器
scp ./fastserver user@your-server:/tmp/
scp -r config/ user@your-server:/tmp/

# 在服务器上安装
ssh user@your-server
sudo mkdir -p /opt/fast-server
sudo mv /tmp/fastserver /opt/fast-server/
sudo mv /tmp/config /opt/fast-server/
sudo chmod +x /opt/fast-server/fastserver
```

### 步骤4: 验证ADC配置

```bash
# 在服务器上运行验证脚本
chmod +x scripts/verify-adc.sh
./scripts/verify-adc.sh
```

### 步骤5: 启动服务

```bash
# 启动服务
sudo systemctl start fast-server

# 启用开机自启
sudo systemctl enable fast-server

# 检查服务状态
sudo systemctl status fast-server

# 查看日志
sudo journalctl -u fast-server -f
```

## 🔧 手动配置（可选）

如果自动化脚本不适用，可以手动配置：

### 1. 创建服务账号

```bash
PROJECT_ID="helical-sol-417708"
SERVICE_ACCOUNT_NAME="fast-server-stt"

gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
    --display-name="Fast Server STT Service Account" \
    --project=$PROJECT_ID
```

### 2. 分配权限

```bash
SERVICE_ACCOUNT_EMAIL="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
    --role="roles/speech.client"
```

### 3. 生成密钥

```bash
gcloud iam service-accounts keys create service-account.json \
    --iam-account=$SERVICE_ACCOUNT_EMAIL
```

### 4. 配置服务器

```bash
# 创建目录
sudo mkdir -p /opt/fast-server/credentials

# 安装密钥
sudo cp service-account.json /opt/fast-server/credentials/
sudo chmod 600 /opt/fast-server/credentials/service-account.json

# 设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS=/opt/fast-server/credentials/service-account.json
```

## 🧪 测试验证

### 1. 测试ADC认证

```bash
# 设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS=/opt/fast-server/credentials/service-account.json

# 测试获取令牌
gcloud auth application-default print-access-token
```

### 2. 测试Speech-to-Text API

```bash
# 获取访问令牌
ACCESS_TOKEN=$(gcloud auth application-default print-access-token)

# 测试API调用
curl -X POST \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "auto_decoding_config": {},
      "language_codes": ["zh-CN"]
    },
    "content": "UklGRiQAAABXQVZFZm10IBAAAAABAAEAgD4AAAB9AAACABAAZGF0YQAAAAA="
  }' \
  "https://speech.googleapis.com/v2/projects/helical-sol-417708/locations/global/recognizers/_:recognize"
```

### 3. 测试应用程序

```bash
# 启动应用程序
cd /opt/fast-server
./fastserver

# 在另一个终端测试STT功能
curl -X POST http://localhost:8080/proto.stt.v1.STTService/TranscribeAudio \
  -H "Content-Type: application/json" \
  -d '{"audio_data": "base64_encoded_audio", "audio_format": 1}'
```

## 🔒 安全最佳实践

### 1. 文件权限

```bash
# 确保凭据文件权限正确
sudo chmod 600 /opt/fast-server/credentials/service-account.json
sudo chown root:root /opt/fast-server/credentials/service-account.json
```

### 2. 网络安全

- 限制服务账号的IP访问范围
- 使用防火墙限制不必要的端口访问
- 定期审计服务账号的使用情况

### 3. 密钥轮换

```bash
# 定期轮换服务账号密钥（建议每90天）
gcloud iam service-accounts keys create new-key.json \
    --iam-account=$SERVICE_ACCOUNT_EMAIL

# 更新服务器上的密钥文件
# 测试新密钥工作正常后删除旧密钥
gcloud iam service-accounts keys delete OLD_KEY_ID \
    --iam-account=$SERVICE_ACCOUNT_EMAIL
```

## 🚨 故障排除

### 常见问题

1. **权限被拒绝**
   ```bash
   # 检查服务账号权限
   gcloud projects get-iam-policy helical-sol-417708 \
     --flatten="bindings[].members" \
     --filter="bindings.members:serviceAccount:YOUR_SERVICE_ACCOUNT"
   ```

2. **凭据文件未找到**
   ```bash
   # 检查环境变量
   echo $GOOGLE_APPLICATION_CREDENTIALS
   
   # 检查文件是否存在
   ls -la $GOOGLE_APPLICATION_CREDENTIALS
   ```

3. **API调用失败**
   ```bash
   # 检查API是否启用
   gcloud services list --enabled --filter="name:speech.googleapis.com"
   
   # 检查项目配额
   gcloud compute project-info describe --project=helical-sol-417708
   ```

## 📊 监控和日志

### 1. 应用程序日志

```bash
# 查看服务日志
sudo journalctl -u fast-server -f

# 查看特定时间范围的日志
sudo journalctl -u fast-server --since "1 hour ago"
```

### 2. Google Cloud审计日志

在Google Cloud Console中启用审计日志：
- Cloud IAM Admin Activity
- Cloud Speech-to-Text Data Access

### 3. 监控指标

设置以下监控指标：
- API调用次数
- 错误率
- 响应时间
- 服务账号使用情况

## 🎯 总结

通过以上步骤，您已经成功在生产环境中配置了ADC认证：

✅ **安全性**: 使用服务账号而非个人凭据
✅ **可靠性**: ADC自动处理令牌刷新
✅ **可维护性**: 标准化的部署流程
✅ **可监控性**: 完整的日志和审计跟踪

现在您的Fast Server可以在生产环境中安全、可靠地使用Google Cloud Speech-to-Text服务了！
